<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PasswordManagerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->password_title ?: $this->user_name, // Platform/Service title
            'username' => $this->username ?: $this->user_name, // Username
            'password' => $this->getDecryptedPassword(), // Include decrypted password
            'level' => $this->getPasswordStrength(), // Password strength level
            'department_id' => $this->department_id,
            'team_id' => $this->team_id,
            'user_id' => $this->user_id,
            'password_title' => $this->password_title ?: $this->user_name, // Frontend expects password_title
            'user_name' => $this->user_name, // Keep this for backward compatibility
            'password_strength' => $this->getPasswordStrength(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Relationships
            'department' => $this->whenLoaded('department', function () {
                return [
                    'id' => $this->department->id,
                    'name' => $this->department->name,
                ];
            }),
            'team' => $this->whenLoaded('team', function () {
                return [
                    'id' => $this->team->id,
                    'name' => $this->team->name,
                ];
            }),
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'eid' => $this->user->eid,
                    'fname' => $this->user->fname,
                    'lname' => $this->user->lname,
                    'full_name' => trim(($this->user->fname ?? '') . ' ' . ($this->user->lname ?? '')),
                    'email' => $this->user->email,
                    'photo' => $this->user->photo ? url('storage/' . $this->user->photo) : null,
                ];
            }),
        ];
    }
}
