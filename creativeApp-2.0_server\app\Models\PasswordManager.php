<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class PasswordManager extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'department_id',
        'team_id',
        'user_id',
        'user_name',
        'password_title',
        'username',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    // protected $hidden = [
    //     'password', // Hide encrypted password from JSON responses
    // ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Automatically encrypt password when setting
     *
     * @param string $value
     * @return void
     */
    public function setPasswordAttribute($value)
    {
        if ($value) {
            $this->attributes['password'] = Crypt::encryptString($value);
        }
    }

    /**
     * Get the decrypted password
     *
     * @return string|null
     */
    public function getDecryptedPassword()
    {
        if (isset($this->attributes['password']) && $this->attributes['password']) {
            try {
                return Crypt::decryptString($this->attributes['password']);
            } catch (\Exception $e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Get password strength level
     *
     * @return string
     */
    public function getPasswordStrength()
    {
        $password = $this->getDecryptedPassword();
        if (!$password) {
            return 'Unknown';
        }

        $score = 0;

        // Length check
        if (strlen($password) >= 12) {
            $score += 2;
        } elseif (strlen($password) >= 8) {
            $score += 1;
        }

        // Character variety checks
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        }
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        }
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        }
        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 1;
        }

        // Additional complexity
        if (strlen($password) >= 16) {
            $score += 1;
        }

        if ($score >= 6) {
            return 'Strong';
        }
        if ($score >= 4) {
            return 'Moderate';
        }
        return 'Weak';
    }

    /**
     * Scope to search by title
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearchByTitle($query, $search)
    {
        return $query->where('user_name', 'like', "%{$search}%");
    }

    // Relationships

    /**
     * Get the department that owns the password manager.
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the team that owns the password manager.
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the user that owns the password manager.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
