<?php

namespace App\Http\Controllers;

use App\Models\PasswordManager;
use App\Http\Requests\StorePasswordManagerRequest;
use App\Http\Requests\UpdatePasswordManagerRequest;
use App\Http\Resources\PasswordManagerResource;
use App\Traits\HasRoleCheck;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PasswordManagerController extends Controller
{
    use HasRoleCheck;

    /**
     * Display a listing of the password managers.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = PasswordManager::with(['department', 'team', 'user']);

            // Apply filters
            if ($request->filled('department_id')) {
                $query->where('department_id', $request->department_id);
            }

            if ($request->filled('team_id')) {
                $query->where('team_id', $request->team_id);
            }

            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            // Search by title
            if ($request->filled('search')) {
                $query->searchByTitle($request->search);
            }

            // Non-admin users can only see their own records
            if (!$this->userIsAdmin()) {
                $user = $this->getAuthenticatedUser();
                if ($user) {
                    $query->where('user_id', $user->id);
                }
            }

            // Order by created_at desc
            $query->orderBy('created_at', 'desc');

            // Paginate results
            $perPage = min($request->get('per_page', 15), 100); // Max 100 per page
            $passwords = $query->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => PasswordManagerResource::collection($passwords->items()),
                'meta' => [
                    'current_page' => $passwords->currentPage(),
                    'per_page' => $passwords->perPage(),
                    'total' => $passwords->total(),
                    'last_page' => $passwords->lastPage(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving password records',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Store a newly created password manager.
     *
     * @param StorePasswordManagerRequest $request
     * @return JsonResponse
     */
    public function store(StorePasswordManagerRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            // Always set user_id to authenticated user for non-admin users
            $user = $this->getAuthenticatedUser();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User not authenticated'
                ], 401);
            }

            if (!$this->userIsAdmin()) {
                $data['user_id'] = $user->id;
            } else {
                // Admin can specify user_id, but default to themselves if not provided
                $data['user_id'] = $data['user_id'] ?? $user->id;
            }

            $passwordManager = PasswordManager::create($data);
            $passwordManager->load(['department', 'team', 'user']);

            return response()->json([
                'status' => 'success',
                'message' => 'Password record created successfully',
                'data' => new PasswordManagerResource($passwordManager)
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error creating password record',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Display the specified password manager.
     *
     * @param PasswordManager $passwordManager
     * @return JsonResponse
     */
    public function show(PasswordManager $passwordManager): JsonResponse
    {
        try {
            // Check authorization
            if (!$this->userIsAdmin()) {
                $user = $this->getAuthenticatedUser();
                if (!$user || $passwordManager->user_id !== $user->id) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unauthorized to view this password record'
                    ], 403);
                }
            }

            $passwordManager->load(['department', 'team', 'user']);

            return response()->json([
                'status' => 'success',
                'data' => new PasswordManagerResource($passwordManager)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving password record',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update the specified password manager.
     *
     * @param UpdatePasswordManagerRequest $request
     * @param PasswordManager $passwordManager
     * @return JsonResponse
     */
    public function update(UpdatePasswordManagerRequest $request, PasswordManager $passwordManager): JsonResponse
    {
        try {
            // Check authorization - creator or admin can update
            if (!$this->userIsAdmin()) {
                $user = $this->getAuthenticatedUser();
                if (!$user || $passwordManager->user_id !== $user->id) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unauthorized. Only the creator or admin can update this password record'
                    ], 403);
                }
            }

            $data = $request->validated();

            // Non-admin users cannot change user_id
            if (!$this->userIsAdmin()) {
                $data['user_id'] = $passwordManager->user_id;
            } else {
                // Admin can specify user_id, but default to current if not provided
                $data['user_id'] = $data['user_id'] ?? $passwordManager->user_id;
            }

            $passwordManager->update($data);
            $passwordManager->load(['department', 'team', 'user']);

            return response()->json([
                'status' => 'success',
                'message' => 'Password record updated successfully',
                'data' => new PasswordManagerResource($passwordManager)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error updating password record',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Remove the specified password manager.
     *
     * @param PasswordManager $passwordManager
     * @return JsonResponse
     */
    public function destroy(PasswordManager $passwordManager): JsonResponse
    {
        try {
            // Check authorization - creator or admin can delete
            if (!$this->userIsAdmin()) {
                $user = $this->getAuthenticatedUser();
                if (!$user || $passwordManager->user_id !== $user->id) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unauthorized. Only the creator or admin can delete this password record'
                    ], 403);
                }
            }

            $passwordManager->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Password record deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error deleting password record',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get decrypted password for a specific record.
     *
     * @param PasswordManager $passwordManager
     * @return JsonResponse
     */
    public function getDecryptedPassword(PasswordManager $passwordManager): JsonResponse
    {
        try {
            // Check authorization - creator or admin can view password
            if (!$this->userIsAdmin()) {
                $user = $this->getAuthenticatedUser();
                if (!$user || $passwordManager->user_id !== $user->id) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unauthorized. Only the creator or admin can view this password'
                    ], 403);
                }
            }

            return response()->json([
                'status' => 'success',
                'password' => $passwordManager->getDecryptedPassword(),
                'strength' => $passwordManager->getPasswordStrength()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving password',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}
