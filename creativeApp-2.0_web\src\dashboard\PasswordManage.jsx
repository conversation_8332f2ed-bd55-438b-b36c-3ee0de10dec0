import  { useState } from 'react';
import PasswordGenerator from '../components/password-manager/PasswordGenerator';
import PasswordCardsTable from '../components/password-manager/PasswordCardsTable';

const PasswordManage = () => {
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [passwordStrength, setPasswordStrength] = useState('Strong Password');

  const handlePasswordGenerated = (password, strength) => {
    setGeneratedPassword(password);
    setPasswordStrength(strength);
  };

  return (
    <div className="bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]">
      {/* Password Generator - Always visible at top */}
      <div className="mb-8">
        <PasswordGenerator onPasswordGenerated={handlePasswordGenerated} />
      </div>

      {/* Password Cards Table - Below generator */}
      <PasswordCardsTable
        generatedPassword={generatedPassword}
        passwordStrength={passwordStrength}
      />
    </div>
  );
};

export default PasswordManage;
