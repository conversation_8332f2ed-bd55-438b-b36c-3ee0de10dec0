/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./node_modules/flowbite/**/*.js",
  ],
  theme: {
    fontFamily: {
      display: ['Inter', 'system-ui', 'sans-serif'],
      body: ['Inter', 'system-ui', 'sans-serif'],
    },
    extend: {
      colors: {
        primary: '#006F99',
        primaryBlue: '#196D92',
        primaryGold: '#FFC638',
        primaryNavy: '#0B333F',
        primarySeafoam: '#DFECF1',
        secondary: '#57B6B2',
        secondaryTeal: '#57B6B2',
        secondaryOrange: '#FF9F19',
        secondaryWarmOrange: '#E17D00',
        secondaryRust: '#BB6228',
        text_primary: '#637381',
        text_secondary: '#8899AB',
        stroke: '#DFE4EA',
        primary_light: '#E0EEF3',
        green: '#22C55E',
        yellow: '#F59E0B',
        red: '#EF4444',
      },
    },
  },
  plugins: [
    require('flowbite/plugin'),
  ],
  darkMode: 'class',
}
