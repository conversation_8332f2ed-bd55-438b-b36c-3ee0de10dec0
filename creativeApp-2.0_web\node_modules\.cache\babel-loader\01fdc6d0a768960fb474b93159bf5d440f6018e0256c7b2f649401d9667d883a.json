{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\AddPasswordCardForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Eye, EyeOff, X, Shield, User, Lock, Building, Users, AlertCircle, CheckCircle, Wand2 } from \"lucide-react\";\nimport { useGetDepartmentDataQuery } from \"../../features/api/departmentApi\";\nimport { useGetTeamDataQuery } from \"../../features/api/teamApi\";\nimport { useCreatePasswordManagerMutation, useUpdatePasswordManagerMutation } from \"../../features/api/passwordManagerApi\";\n\n// Main component for the Add Password Card form\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddPasswordCardForm = ({\n  onCancel,\n  generatedPassword,\n  passwordStrength,\n  editData = null,\n  // For editing existing password\n  onSuccess // Callback after successful save\n}) => {\n  _s();\n  var _departmentsData$data, _teamsData$data, _teamsData$data$filte;\n  // API hooks\n  const {\n    data: departmentsData\n  } = useGetDepartmentDataQuery({\n    page: 1,\n    per_page: 100\n  });\n  const {\n    data: teamsData\n  } = useGetTeamDataQuery({\n    page: 1,\n    per_page: 100\n  });\n  const [createPasswordManager, {\n    isLoading: isCreating\n  }] = useCreatePasswordManagerMutation();\n  const [updatePasswordManager, {\n    isLoading: isUpdating\n  }] = useUpdatePasswordManagerMutation();\n\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    password_title: \"\",\n    username: \"\",\n    password: \"\",\n    department_id: \"\",\n    team_id: \"\"\n  });\n\n  // Initialize form data for editing or reset for new entries\n  useEffect(() => {\n    if (editData) {\n      setFormData({\n        password_title: editData.password_title || editData.title || \"\",\n        username: editData.username || \"\",\n        password: \"\",\n        // Don't pre-fill password for security\n        department_id: editData.department_id || \"\",\n        team_id: editData.team_id || \"\"\n      });\n    } else {\n      // FIXED: Reset form completely when not editing (new entry)\n      setFormData({\n        password_title: \"\",\n        username: \"\",\n        password: \"\",\n        department_id: \"\",\n        team_id: \"\"\n      });\n      setErrors({});\n      setFormError(\"\");\n      setShowPassword(false);\n    }\n  }, [editData]);\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [focusedField, setFocusedField] = useState(null);\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === \"department_id\") {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n        team_id: \"\"\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: \"\"\n      }));\n    }\n\n    // Clear form error when user starts typing\n    if (formError) {\n      setFormError(\"\");\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = password => {\n    if (!password) return \"Weak Password\";\n    let score = 0;\n    if (password.length >= 12) score += 2;else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return \"Strong Password\";\n    if (score >= 4) return \"Moderate Password\";\n    return \"Weak Password\";\n  };\n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = strength => {\n    switch (strength) {\n      case \"Strong Password\":\n        return \"bg-emerald-50 text-emerald-700 border-emerald-200\";\n      case \"Moderate Password\":\n        return \"bg-amber-50 text-amber-700 border-amber-200\";\n      case \"Weak Password\":\n        return \"bg-red-50 text-red-700 border-red-200\";\n      default:\n        return \"bg-gray-50 text-gray-600 border-gray-200\";\n    }\n  };\n\n  // Get strength progress and color\n  const getStrengthProgress = strength => {\n    switch (strength) {\n      case \"Strong Password\":\n        return {\n          width: \"100%\",\n          color: \"bg-emerald-500\"\n        };\n      case \"Moderate Password\":\n        return {\n          width: \"66%\",\n          color: \"bg-amber-500\"\n        };\n      case \"Weak Password\":\n        return {\n          width: \"33%\",\n          color: \"bg-red-500\"\n        };\n      default:\n        return {\n          width: \"0%\",\n          color: \"bg-gray-300\"\n        };\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.password_title.trim()) newErrors.password_title = \"Platform title is required\";\n    if (!formData.username.trim()) newErrors.username = \"Username is required\";\n    if (!formData.password.trim()) newErrors.password = \"Password is required\";\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setFormError(\"\");\n    setIsLoading(true);\n    if (validateForm()) {\n      try {\n        const submitData = {\n          password_title: formData.password_title,\n          username: formData.username,\n          password: formData.password,\n          department_id: formData.department_id || null,\n          team_id: formData.team_id || null\n        };\n        let newPasswordId = null;\n        if (editData) {\n          // Update existing password\n          await updatePasswordManager({\n            id: editData.id,\n            ...submitData\n          }).unwrap();\n        } else {\n          var _result$data;\n          // Create new password\n          const result = await createPasswordManager(submitData).unwrap();\n          newPasswordId = result === null || result === void 0 ? void 0 : (_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.id;\n        }\n\n        // FIXED: Reset form after successful submission\n        setFormData({\n          password_title: \"\",\n          username: \"\",\n          password: \"\",\n          department_id: \"\",\n          team_id: \"\"\n        });\n        setErrors({});\n        setFormError(\"\");\n        setShowPassword(false);\n\n        // FIXED: Call success callback to refresh data and close modal\n        // The success callback will handle the toast notification\n        if (onSuccess) {\n          onSuccess(newPasswordId);\n        }\n      } catch (error) {\n        var _error$data;\n        console.error(\"Error saving password:\", error);\n        setFormError((error === null || error === void 0 ? void 0 : (_error$data = error.data) === null || _error$data === void 0 ? void 0 : _error$data.message) || \"Failed to save password. Please try again.\");\n      }\n    } else {\n      setFormError(\"Please fill out all required fields before saving.\");\n    }\n    setIsLoading(false);\n  };\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  };\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n  const hasErrors = Object.keys(errors).length > 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-primary px-6 py-4 text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-white/20 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold\",\n                  children: \"Add New Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white-100 text-sm\",\n                  children: \"Secure your credentials with us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onCancel,\n            className: \"p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 overflow-y-auto max-h-[calc(90vh-200px)]\",\n        children: [formError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl flex items-start space-x-3 animate-in slide-in-from-top-2\",\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            className: \"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-left font-medium text-red-800\",\n              children: \"Action Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-700 text-sm\",\n              children: formError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4]\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 bg-gray-50 rounded-xl border border-gray-200 ${hasErrors ? \"h-[340px]\" : \"h-[280px]\"}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Building, {\n                  className: \"w-5 h-5 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-800\",\n                  children: \"Organization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \" text-left block text-sm font-medium text-gray-700 mb-2\",\n                    children: [\"Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 34\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"department_id\",\n                    value: formData.department_id,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"department_id\"),\n                    onBlur: () => setFocusedField(null),\n                    className: `w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none focus:ring-0 ${errors.department_id ? \"border-red-300 focus:border-red-500\" : focusedField === \"department_id\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Department\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 23\n                    }, this), departmentsData === null || departmentsData === void 0 ? void 0 : (_departmentsData$data = departmentsData.data) === null || _departmentsData$data === void 0 ? void 0 : _departmentsData$data.map(dept => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: dept.id,\n                      children: dept.name\n                    }, dept.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 21\n                  }, this), errors.department_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-2 text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-4 h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 25\n                    }, this), errors.department_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-sm font-medium text-gray-700 mb-2\",\n                    children: [\"Team \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 28\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"team_id\",\n                    value: formData.team_id,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"team_id\"),\n                    onBlur: () => setFocusedField(null),\n                    disabled: !formData.department_id,\n                    className: `w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none ${!formData.department_id ? \"bg-gray-100 border-gray-200 cursor-not-allowed\" : errors.team_id ? \"border-red-300 focus:border-red-500\" : focusedField === \"team_id\" ? \"border-primary-500 shadow-lg shadow-primary-100\" : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Team\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 23\n                    }, this), formData.department_id && (teamsData === null || teamsData === void 0 ? void 0 : (_teamsData$data = teamsData.data) === null || _teamsData$data === void 0 ? void 0 : (_teamsData$data$filte = _teamsData$data.filter(team => team.department_id === parseInt(formData.department_id))) === null || _teamsData$data$filte === void 0 ? void 0 : _teamsData$data$filte.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: team.id,\n                      children: team.name\n                    }, team.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 27\n                    }, this)))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this), errors.team_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-2 text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-4 h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 25\n                    }, this), errors.team_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), !formData.department_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-2 text-sm text-gray-500 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Users, {\n                      className: \"w-4 h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 25\n                    }, this), \"Please select a department first\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 bg-gray-50 rounded-xl border border-gray-200 ${hasErrors ? \"h-[340px]\" : \"h-[280px]\"}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"w-5 h-5 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-800\",\n                  children: \"Account Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col h-full space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-sm font-medium text-gray-700 mb-2\",\n                    children: [\"Platform/Service Title\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"password_title\",\n                    value: formData.password_title,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"password_title\"),\n                    onBlur: () => setFocusedField(null),\n                    placeholder: \"e.g., Gmail, GitHub, AWS Console\",\n                    className: `w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none ${errors.password_title ? \"border-red-300 focus:border-red-500\" : focusedField === \"password_title\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this), errors.password_title && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-2 text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-4 h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 25\n                    }, this), errors.password_title]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-left block text-sm font-medium text-gray-700 mb-2\",\n                    children: [\"Username \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"username\",\n                    value: formData.username,\n                    onChange: handleInputChange,\n                    onFocus: () => setFocusedField(\"username\"),\n                    onBlur: () => setFocusedField(null),\n                    placeholder: \"Enter username or email\",\n                    className: `w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none ${errors.username ? \"border-red-300 focus:border-red-500\" : focusedField === \"username\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary hover:border-gray-300\"}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-2 text-sm text-red-600 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                      className: \"w-4 h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 25\n                    }, this), errors.username]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-gray-50 rounded-xl border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Lock, {\n                className: \"w-5 h-5 text-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-gray-800\",\n                children: \"Security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-left block text-sm font-medium text-gray-700 mb-2\",\n                children: [\"Password \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 28\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? \"text\" : \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  onFocus: () => setFocusedField(\"password\"),\n                  onBlur: () => setFocusedField(null),\n                  placeholder: \"Enter password\",\n                  className: `w-full px-4 py-3 pr-32 rounded-lg border-2 transition-all duration-200 focus:outline-none ${errors.password ? \"border-red-300 focus:border-red-500\" : focusedField === \"password\" ? \"border-primary-500 shadow-lg shadow-primary/10-100\" : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: useGeneratedPassword,\n                    disabled: !generatedPassword,\n                    className: `px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1 ${generatedPassword ? \"bg-primary/10-100 text-white-700 hover:bg-primary-200\" : \"bg-gray-100 text-gray-400 cursor-not-allowed\"}`,\n                    title: \"Use generated password\",\n                    children: [/*#__PURE__*/_jsxDEV(Wand2, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Use\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: togglePasswordVisibility,\n                    className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200\",\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 503,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), errors.password]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: \"Password Strength\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-sm font-medium px-2 py-1 rounded-full ${getStrengthColor(formData.strength)}`,\n                    children: formData.strength\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-200 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `h-2 rounded-full transition-all duration-300 ${getStrengthProgress(formData.strength).color}`,\n                    style: {\n                      width: getStrengthProgress(formData.strength).width\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"All fields marked with * are required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onCancel,\n            className: \"px-6 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 font-medium\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleSubmit,\n            disabled: isLoading || isCreating || isUpdating,\n            className: \"px-6 py-2.5 bg-primary hover:bg-primary/90 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-500 focus:ring-offset-2 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n            children: isLoading || isCreating || isUpdating ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: editData ? 'Updating...' : 'Saving...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Shield, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: editData ? 'Update Password' : 'Save Password'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n};\n_s(AddPasswordCardForm, \"AIYFCvAqaSEHdLE+EBuu18PKehw=\", false, function () {\n  return [useGetDepartmentDataQuery, useGetTeamDataQuery, useCreatePasswordManagerMutation, useUpdatePasswordManagerMutation];\n});\n_c = AddPasswordCardForm;\nexport default AddPasswordCardForm;\nvar _c;\n$RefreshReg$(_c, \"AddPasswordCardForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Eye", "Eye<PERSON>ff", "X", "Shield", "User", "Lock", "Building", "Users", "AlertCircle", "CheckCircle", "Wand2", "useGetDepartmentDataQuery", "useGetTeamDataQuery", "useCreatePasswordManagerMutation", "useUpdatePasswordManagerMutation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddPasswordCardForm", "onCancel", "generatedPassword", "passwordStrength", "editData", "onSuccess", "_s", "_departmentsData$data", "_teamsData$data", "_teamsData$data$filte", "data", "departmentsData", "page", "per_page", "teamsData", "createPasswordManager", "isLoading", "isCreating", "updatePasswordManager", "isUpdating", "formData", "setFormData", "password_title", "username", "password", "department_id", "team_id", "title", "setErrors", "setFormError", "setShowPassword", "showPassword", "errors", "formError", "setIsLoading", "focusedField", "setFocusedField", "prev", "strength", "handleInputChange", "e", "name", "value", "target", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "getStrengthProgress", "width", "color", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "newPasswordId", "id", "unwrap", "_result$data", "result", "error", "_error$data", "console", "message", "useGeneratedPassword", "togglePasswordVisibility", "hasErrors", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "onFocus", "onBlur", "map", "dept", "disabled", "filter", "team", "parseInt", "type", "placeholder", "style", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordCardForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport {\n  Eye,\n  EyeOff,\n  X,\n  Shield,\n  User,\n  Lock,\n  Building,\n  Users,\n  AlertCircle,\n  CheckCircle,\n  Wand2,\n} from \"lucide-react\";\nimport { useGetDepartmentDataQuery } from \"../../features/api/departmentApi\";\nimport { useGetTeamDataQuery } from \"../../features/api/teamApi\";\nimport {\n  useCreatePasswordManagerMutation,\n  useUpdatePasswordManagerMutation\n} from \"../../features/api/passwordManagerApi\";\n\n// Main component for the Add Password Card form\nconst AddPasswordCardForm = ({\n  onCancel,\n  generatedPassword,\n  passwordStrength,\n  editData = null, // For editing existing password\n  onSuccess, // Callback after successful save\n}) => {\n  // API hooks\n  const { data: departmentsData } = useGetDepartmentDataQuery({ page: 1, per_page: 100 });\n  const { data: teamsData } = useGetTeamDataQuery({ page: 1, per_page: 100 });\n  const [createPasswordManager, { isLoading: isCreating }] = useCreatePasswordManagerMutation();\n  const [updatePasswordManager, { isLoading: isUpdating }] = useUpdatePasswordManagerMutation();\n\n  // State to hold form data\n  const [formData, setFormData] = useState({\n    password_title: \"\",\n    username: \"\",\n    password: \"\",\n    department_id: \"\",\n    team_id: \"\",\n  });\n\n  // Initialize form data for editing or reset for new entries\n  useEffect(() => {\n    if (editData) {\n      setFormData({\n        password_title: editData.password_title || editData.title || \"\",\n        username: editData.username || \"\",\n        password: \"\", // Don't pre-fill password for security\n        department_id: editData.department_id || \"\",\n        team_id: editData.team_id || \"\",\n      });\n    } else {\n      // FIXED: Reset form completely when not editing (new entry)\n      setFormData({\n        password_title: \"\",\n        username: \"\",\n        password: \"\",\n        department_id: \"\",\n        team_id: \"\",\n      });\n      setErrors({});\n      setFormError(\"\");\n      setShowPassword(false);\n    }\n  }, [editData]);\n\n  // State for showing/hiding the password, individual field errors, and a general form error\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formError, setFormError] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [focusedField, setFocusedField] = useState(null);\n\n  // Effect to update the form's password field when a new password is generated\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData((prev) => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength,\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  // Handles input changes for all form fields\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n\n    // Reset team selection when the department changes\n    if (name === \"department_id\") {\n      setFormData((prev) => ({ ...prev, [name]: value, team_id: \"\" }));\n    } else {\n      setFormData((prev) => ({ ...prev, [name]: value }));\n    }\n\n    // Clear the error for a field when the user starts typing\n    if (errors[name]) {\n      setErrors((prev) => ({ ...prev, [name]: \"\" }));\n    }\n\n    // Clear form error when user starts typing\n    if (formError) {\n      setFormError(\"\");\n    }\n  };\n\n  // Calculates the strength of a given password\n  const calculatePasswordStrength = (password) => {\n    if (!password) return \"Weak Password\";\n    let score = 0;\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return \"Strong Password\";\n    if (score >= 4) return \"Moderate Password\";\n    return \"Weak Password\";\n  };\n\n  // Returns Tailwind CSS classes based on password strength for styling\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case \"Strong Password\":\n        return \"bg-emerald-50 text-emerald-700 border-emerald-200\";\n      case \"Moderate Password\":\n        return \"bg-amber-50 text-amber-700 border-amber-200\";\n      case \"Weak Password\":\n        return \"bg-red-50 text-red-700 border-red-200\";\n      default:\n        return \"bg-gray-50 text-gray-600 border-gray-200\";\n    }\n  };\n\n  // Get strength progress and color\n  const getStrengthProgress = (strength) => {\n    switch (strength) {\n      case \"Strong Password\":\n        return { width: \"100%\", color: \"bg-emerald-500\" };\n      case \"Moderate Password\":\n        return { width: \"66%\", color: \"bg-amber-500\" };\n      case \"Weak Password\":\n        return { width: \"33%\", color: \"bg-red-500\" };\n      default:\n        return { width: \"0%\", color: \"bg-gray-300\" };\n    }\n  };\n\n  // Validates the form fields before submission\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.password_title.trim())\n      newErrors.password_title = \"Platform title is required\";\n    if (!formData.username.trim())\n      newErrors.username = \"Username is required\";\n    if (!formData.password.trim())\n      newErrors.password = \"Password is required\";\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handles the form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setFormError(\"\");\n    setIsLoading(true);\n\n    if (validateForm()) {\n      try {\n        const submitData = {\n          password_title: formData.password_title,\n          username: formData.username,\n          password: formData.password,\n          department_id: formData.department_id || null,\n          team_id: formData.team_id || null,\n        };\n\n        let newPasswordId = null;\n\n        if (editData) {\n          // Update existing password\n          await updatePasswordManager({ id: editData.id, ...submitData }).unwrap();\n        } else {\n          // Create new password\n          const result = await createPasswordManager(submitData).unwrap();\n          newPasswordId = result?.data?.id;\n        }\n\n        // FIXED: Reset form after successful submission\n        setFormData({\n          password_title: \"\",\n          username: \"\",\n          password: \"\",\n          department_id: \"\",\n          team_id: \"\",\n        });\n        setErrors({});\n        setFormError(\"\");\n        setShowPassword(false);\n\n        // FIXED: Call success callback to refresh data and close modal\n        // The success callback will handle the toast notification\n        if (onSuccess) {\n          onSuccess(newPasswordId);\n        }\n\n      } catch (error) {\n        console.error(\"Error saving password:\", error);\n        setFormError(error?.data?.message || \"Failed to save password. Please try again.\");\n      }\n    } else {\n      setFormError(\"Please fill out all required fields before saving.\");\n    }\n\n    setIsLoading(false);\n  };\n\n  // Sets the form password to the generated password\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData((prev) => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength,\n      }));\n    }\n  };\n\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n\n  const hasErrors = Object.keys(errors).length > 0;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300\">\n      <div className=\"bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden animate-in slide-in-from-bottom-4 duration-500\">\n        {/* Header */}\n        <div className=\"bg-primary px-6 py-4 text-white\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-white/20 rounded-lg\">\n                <Shield className=\"w-6 h-6\" />\n              </div>\n              <div>\n                <div className=\"text-left\">\n                  <h2 className=\"text-xl font-semibold\">Add New Password</h2>\n                  <p className=\"text-white-100 text-sm\">\n                    Secure your credentials with us\n                  </p>\n                </div>\n              </div>\n            </div>\n            <button\n              onClick={onCancel}\n              className=\"p-2 hover:bg-white/20 rounded-lg transition-colors duration-200\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Form Content */}\n        <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-200px)]\">\n          {/* Error Message */}\n          {formError && (\n            <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl flex items-start space-x-3 animate-in slide-in-from-top-2\">\n              <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0\" />\n              <div>\n                <p className=\"text-left font-medium text-red-800\">\n                  Action Required\n                </p>\n                <p className=\"text-red-700 text-sm\">{formError}</p>\n              </div>\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Department & Team Section */}\n            <div className=\"space-y-4]\">\n              <div\n                className={`p-4 bg-gray-50 rounded-xl border border-gray-200 ${\n                  hasErrors ? \"h-[340px]\" : \"h-[280px]\"\n                }`}\n              >\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  <Building className=\"w-5 h-5 text-gray-600\" />\n                  <h3 className=\"font-medium text-gray-800\">Organization</h3>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\" text-left block text-sm font-medium text-gray-700 mb-2\">\n                      Department <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      name=\"department_id\"\n                      value={formData.department_id}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"department_id\")}\n                      onBlur={() => setFocusedField(null)}\n                      className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none focus:ring-0 ${\n                        errors.department_id\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"department_id\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    >\n                      <option value=\"\">Select Department</option>\n                      {departmentsData?.data?.map((dept) => (\n                        <option key={dept.id} value={dept.id}>\n                          {dept.name}\n                        </option>\n                      ))}\n                    </select>\n                    {errors.department_id && (\n                      <p className=\"mt-2 text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-4 h-4 mr-1\" />\n                        {errors.department_id}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"text-left block text-sm font-medium text-gray-700 mb-2\">\n                      Team <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      name=\"team_id\"\n                      value={formData.team_id}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"team_id\")}\n                      onBlur={() => setFocusedField(null)}\n                      disabled={!formData.department_id}\n                      className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none ${\n                        !formData.department_id\n                          ? \"bg-gray-100 border-gray-200 cursor-not-allowed\"\n                          : errors.team_id\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"team_id\"\n                          ? \"border-primary-500 shadow-lg shadow-primary-100\"\n                          : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"\n                      }`}\n                    >\n                      <option value=\"\">Select Team</option>\n                      {formData.department_id &&\n                        teamsData?.data?.filter(team => team.department_id === parseInt(formData.department_id))?.map((team) => (\n                          <option key={team.id} value={team.id}>\n                            {team.name}\n                          </option>\n                        ))}\n                    </select>\n                    {errors.team_id && (\n                      <p className=\"mt-2 text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-4 h-4 mr-1\" />\n                        {errors.team_id}\n                      </p>\n                    )}\n                    {!formData.department_id && (\n                      <p className=\"mt-2 text-sm text-gray-500 flex items-center\">\n                        <Users className=\"w-4 h-4 mr-1\" />\n                        Please select a department first\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Platform & Username Section */}\n            <div className=\"space-y-4\">\n              <div\n                className={`p-4 bg-gray-50 rounded-xl border border-gray-200 ${\n                  hasErrors ? \"h-[340px]\" : \"h-[280px]\"\n                }`}\n              >\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  <User className=\"w-5 h-5 text-gray-600\" />\n                  <h3 className=\"font-medium text-gray-800\">Account Details</h3>\n                </div>\n\n                <div className=\"flex flex-col h-full space-y-4\">\n                  <div>\n                    <label className=\"text-left block text-sm font-medium text-gray-700 mb-2\">\n                      Platform/Service Title{\" \"}\n                      <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"password_title\"\n                      value={formData.password_title}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"password_title\")}\n                      onBlur={() => setFocusedField(null)}\n                      placeholder=\"e.g., Gmail, GitHub, AWS Console\"\n                      className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none ${\n                        errors.password_title\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"password_title\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    />\n                    {errors.password_title && (\n                      <p className=\"mt-2 text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-4 h-4 mr-1\" />\n                        {errors.password_title}\n                      </p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"text-left block text-sm font-medium text-gray-700 mb-2\">\n                      Username <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"username\"\n                      value={formData.username}\n                      onChange={handleInputChange}\n                      onFocus={() => setFocusedField(\"username\")}\n                      onBlur={() => setFocusedField(null)}\n                      placeholder=\"Enter username or email\"\n                      className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 focus:outline-none ${\n                        errors.username\n                          ? \"border-red-300 focus:border-red-500\"\n                          : focusedField === \"username\"\n                          ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                          : \"border-gray-200 focus:border-primary hover:border-gray-300\"\n                      }`}\n                    />\n                    {errors.username && (\n                      <p className=\"mt-2 text-sm text-red-600 flex items-center\">\n                        <AlertCircle className=\"w-4 h-4 mr-1\" />\n                        {errors.username}\n                      </p>\n                    )}\n                  </div>\n\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Password Section */}\n          <div className=\"mt-6\">\n            <div className=\"p-4 bg-gray-50 rounded-xl border border-gray-200\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <Lock className=\"w-5 h-5 text-gray-600\" />\n                <h3 className=\"font-medium text-gray-800\">Security</h3>\n              </div>\n\n              <div>\n                <label className=\"text-left block text-sm font-medium text-gray-700 mb-2\">\n                  Password <span className=\"text-red-500\">*</span>\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showPassword ? \"text\" : \"password\"}\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    onFocus={() => setFocusedField(\"password\")}\n                    onBlur={() => setFocusedField(null)}\n                    placeholder=\"Enter password\"\n                    className={`w-full px-4 py-3 pr-32 rounded-lg border-2 transition-all duration-200 focus:outline-none ${\n                      errors.password\n                        ? \"border-red-300 focus:border-red-500\"\n                        : focusedField === \"password\"\n                        ? \"border-primary-500 shadow-lg shadow-primary/10-100\"\n                        : \"border-gray-200 focus:border-primary-500 hover:border-gray-300\"\n                    }`}\n                  />\n\n                  <div className=\"absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-1\">\n                    <button\n                      type=\"button\"\n                      onClick={useGeneratedPassword}\n                      disabled={!generatedPassword}\n                      className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 flex items-center space-x-1 ${\n                        generatedPassword\n                          ? \"bg-primary/10-100 text-white-700 hover:bg-primary-200\"\n                          : \"bg-gray-100 text-gray-400 cursor-not-allowed\"\n                      }`}\n                      title=\"Use generated password\"\n                    >\n                      <Wand2 className=\"w-3 h-3\" />\n                      <span>Use</span>\n                    </button>\n\n                    <button\n                      type=\"button\"\n                      onClick={togglePasswordVisibility}\n                      className=\"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200\"\n                    >\n                      {showPassword ? (\n                        <EyeOff className=\"w-4 h-4\" />\n                      ) : (\n                        <Eye className=\"w-4 h-4\" />\n                      )}\n                    </button>\n                  </div>\n                </div>\n\n                {errors.password && (\n                  <p className=\"mt-2 text-sm text-red-600 flex items-center\">\n                    <AlertCircle className=\"w-4 h-4 mr-1\" />\n                    {errors.password}\n                  </p>\n                )}\n\n                {/* Password Strength Indicator */}\n                {formData.password && (\n                  <div className=\"mt-4 space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm font-medium text-gray-700\">\n                        Password Strength\n                      </span>\n                      <span\n                        className={`text-sm font-medium px-2 py-1 rounded-full ${getStrengthColor(\n                          formData.strength\n                        )}`}\n                      >\n                        {formData.strength}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className={`h-2 rounded-full transition-all duration-300 ${\n                          getStrengthProgress(formData.strength).color\n                        }`}\n                        style={{\n                          width: getStrengthProgress(formData.strength).width,\n                        }}\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n            <CheckCircle className=\"w-4 h-4\" />\n            <span>All fields marked with * are required</span>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"px-6 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 font-medium\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"button\"\n              onClick={handleSubmit}\n              disabled={isLoading || isCreating || isUpdating}\n              className=\"px-6 py-2.5 bg-primary hover:bg-primary/90 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-500 focus:ring-offset-2 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n            >\n              {(isLoading || isCreating || isUpdating) ? (\n                <>\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>{editData ? 'Updating...' : 'Saving...'}</span>\n                </>\n              ) : (\n                <>\n                  <Shield className=\"w-4 h-4\" />\n                  <span>{editData ? 'Update Password' : 'Save Password'}</span>\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddPasswordCardForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,MAAM,EACNC,CAAC,EACDC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,WAAW,EACXC,KAAK,QACA,cAAc;AACrB,SAASC,yBAAyB,QAAQ,kCAAkC;AAC5E,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SACEC,gCAAgC,EAChCC,gCAAgC,QAC3B,uCAAuC;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,QAAQ;EACRC,iBAAiB;EACjBC,gBAAgB;EAChBC,QAAQ,GAAG,IAAI;EAAE;EACjBC,SAAS,CAAE;AACb,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA;EACJ;EACA,MAAM;IAAEC,IAAI,EAAEC;EAAgB,CAAC,GAAGnB,yBAAyB,CAAC;IAAEoB,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAI,CAAC,CAAC;EACvF,MAAM;IAAEH,IAAI,EAAEI;EAAU,CAAC,GAAGrB,mBAAmB,CAAC;IAAEmB,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAI,CAAC,CAAC;EAC3E,MAAM,CAACE,qBAAqB,EAAE;IAAEC,SAAS,EAAEC;EAAW,CAAC,CAAC,GAAGvB,gCAAgC,CAAC,CAAC;EAC7F,MAAM,CAACwB,qBAAqB,EAAE;IAAEF,SAAS,EAAEG;EAAW,CAAC,CAAC,GAAGxB,gCAAgC,CAAC,CAAC;;EAE7F;EACA,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC;IACvC2C,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIwB,QAAQ,EAAE;MACZiB,WAAW,CAAC;QACVC,cAAc,EAAElB,QAAQ,CAACkB,cAAc,IAAIlB,QAAQ,CAACuB,KAAK,IAAI,EAAE;QAC/DJ,QAAQ,EAAEnB,QAAQ,CAACmB,QAAQ,IAAI,EAAE;QACjCC,QAAQ,EAAE,EAAE;QAAE;QACdC,aAAa,EAAErB,QAAQ,CAACqB,aAAa,IAAI,EAAE;QAC3CC,OAAO,EAAEtB,QAAQ,CAACsB,OAAO,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAL,WAAW,CAAC;QACVC,cAAc,EAAE,EAAE;QAClBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,aAAa,EAAE,EAAE;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC;MACFE,SAAS,CAAC,CAAC,CAAC,CAAC;MACbC,YAAY,CAAC,EAAE,CAAC;MAChBC,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAAC1B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM,CAAC2B,YAAY,EAAED,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,MAAM,EAAEJ,SAAS,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACsD,SAAS,EAAEJ,YAAY,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqC,SAAS,EAAEkB,YAAY,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIsB,iBAAiB,EAAE;MACrBmB,WAAW,CAAEgB,IAAI,KAAM;QACrB,GAAGA,IAAI;QACPb,QAAQ,EAAEtB,iBAAiB;QAC3BoC,QAAQ,EAAEnC;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACD,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;;EAEzC;EACA,MAAMoC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,eAAe,EAAE;MAC5BpB,WAAW,CAAEgB,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAE,CAACI,IAAI,GAAGC,KAAK;QAAEhB,OAAO,EAAE;MAAG,CAAC,CAAC,CAAC;IAClE,CAAC,MAAM;MACLL,WAAW,CAAEgB,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAE,CAACI,IAAI,GAAGC;MAAM,CAAC,CAAC,CAAC;IACrD;;IAEA;IACA,IAAIV,MAAM,CAACS,IAAI,CAAC,EAAE;MAChBb,SAAS,CAAES,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAE,CAACI,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAChD;;IAEA;IACA,IAAIR,SAAS,EAAE;MACbJ,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMe,yBAAyB,GAAIpB,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,eAAe;IACrC,IAAIqB,KAAK,GAAG,CAAC;IACb,IAAIrB,QAAQ,CAACsB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC,CAAC,KACjC,IAAIrB,QAAQ,CAACsB,MAAM,IAAI,CAAC,EAAED,KAAK,IAAI,CAAC;IACzC,IAAI,OAAO,CAACE,IAAI,CAACvB,QAAQ,CAAC,EAAEqB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACvB,QAAQ,CAAC,EAAEqB,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACvB,QAAQ,CAAC,EAAEqB,KAAK,IAAI,CAAC;IACtC,IAAI,cAAc,CAACE,IAAI,CAACvB,QAAQ,CAAC,EAAEqB,KAAK,IAAI,CAAC;IAC7C,IAAIrB,QAAQ,CAACsB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC;IACrC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAIV,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO,mDAAmD;MAC5D,KAAK,mBAAmB;QACtB,OAAO,6CAA6C;MACtD,KAAK,eAAe;QAClB,OAAO,uCAAuC;MAChD;QACE,OAAO,0CAA0C;IACrD;EACF,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAIX,QAAQ,IAAK;IACxC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO;UAAEY,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAiB,CAAC;MACnD,KAAK,mBAAmB;QACtB,OAAO;UAAED,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAe,CAAC;MAChD,KAAK,eAAe;QAClB,OAAO;UAAED,KAAK,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAa,CAAC;MAC9C;QACE,OAAO;UAAED,KAAK,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAc,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,IAAI,CAACjC,QAAQ,CAACE,cAAc,CAACgC,IAAI,CAAC,CAAC,EACjCD,SAAS,CAAC/B,cAAc,GAAG,4BAA4B;IACzD,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC+B,IAAI,CAAC,CAAC,EAC3BD,SAAS,CAAC9B,QAAQ,GAAG,sBAAsB;IAC7C,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAAC8B,IAAI,CAAC,CAAC,EAC3BD,SAAS,CAAC7B,QAAQ,GAAG,sBAAsB;IAC7CI,SAAS,CAACyB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACP,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMW,YAAY,GAAG,MAAOjB,CAAC,IAAK;IAChCA,CAAC,CAACkB,cAAc,CAAC,CAAC;IAClB7B,YAAY,CAAC,EAAE,CAAC;IAChBK,YAAY,CAAC,IAAI,CAAC;IAElB,IAAIkB,YAAY,CAAC,CAAC,EAAE;MAClB,IAAI;QACF,MAAMO,UAAU,GAAG;UACjBrC,cAAc,EAAEF,QAAQ,CAACE,cAAc;UACvCC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;UAC3BC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;UAC3BC,aAAa,EAAEL,QAAQ,CAACK,aAAa,IAAI,IAAI;UAC7CC,OAAO,EAAEN,QAAQ,CAACM,OAAO,IAAI;QAC/B,CAAC;QAED,IAAIkC,aAAa,GAAG,IAAI;QAExB,IAAIxD,QAAQ,EAAE;UACZ;UACA,MAAMc,qBAAqB,CAAC;YAAE2C,EAAE,EAAEzD,QAAQ,CAACyD,EAAE;YAAE,GAAGF;UAAW,CAAC,CAAC,CAACG,MAAM,CAAC,CAAC;QAC1E,CAAC,MAAM;UAAA,IAAAC,YAAA;UACL;UACA,MAAMC,MAAM,GAAG,MAAMjD,qBAAqB,CAAC4C,UAAU,CAAC,CAACG,MAAM,CAAC,CAAC;UAC/DF,aAAa,GAAGI,MAAM,aAANA,MAAM,wBAAAD,YAAA,GAANC,MAAM,CAAEtD,IAAI,cAAAqD,YAAA,uBAAZA,YAAA,CAAcF,EAAE;QAClC;;QAEA;QACAxC,WAAW,CAAC;UACVC,cAAc,EAAE,EAAE;UAClBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,EAAE;UACjBC,OAAO,EAAE;QACX,CAAC,CAAC;QACFE,SAAS,CAAC,CAAC,CAAC,CAAC;QACbC,YAAY,CAAC,EAAE,CAAC;QAChBC,eAAe,CAAC,KAAK,CAAC;;QAEtB;QACA;QACA,IAAIzB,SAAS,EAAE;UACbA,SAAS,CAACuD,aAAa,CAAC;QAC1B;MAEF,CAAC,CAAC,OAAOK,KAAK,EAAE;QAAA,IAAAC,WAAA;QACdC,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CpC,YAAY,CAAC,CAAAoC,KAAK,aAALA,KAAK,wBAAAC,WAAA,GAALD,KAAK,CAAEvD,IAAI,cAAAwD,WAAA,uBAAXA,WAAA,CAAaE,OAAO,KAAI,4CAA4C,CAAC;MACpF;IACF,CAAC,MAAM;MACLvC,YAAY,CAAC,oDAAoD,CAAC;IACpE;IAEAK,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMmC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAInE,iBAAiB,EAAE;MACrBmB,WAAW,CAAEgB,IAAI,KAAM;QACrB,GAAGA,IAAI;QACPb,QAAQ,EAAEtB,iBAAiB;QAC3BoC,QAAQ,EAAEnC;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMmE,wBAAwB,GAAGA,CAAA,KAAMxC,eAAe,CAAC,CAACC,YAAY,CAAC;EAErE,MAAMwC,SAAS,GAAGhB,MAAM,CAACC,IAAI,CAACxB,MAAM,CAAC,CAACc,MAAM,GAAG,CAAC;EAEhD,oBACEjD,OAAA;IAAK2E,SAAS,EAAC,sHAAsH;IAAAC,QAAA,eACnI5E,OAAA;MAAK2E,SAAS,EAAC,8HAA8H;MAAAC,QAAA,gBAE3I5E,OAAA;QAAK2E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9C5E,OAAA;UAAK2E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5E,OAAA;YAAK2E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5E,OAAA;cAAK2E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzC5E,OAAA,CAACb,MAAM;gBAACwF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNhF,OAAA;cAAA4E,QAAA,eACE5E,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAI2E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3DhF,OAAA;kBAAG2E,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhF,OAAA;YACEiF,OAAO,EAAE7E,QAAS;YAClBuE,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAE3E5E,OAAA,CAACd,CAAC;cAACyF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhF,OAAA;QAAK2E,SAAS,EAAC,8CAA8C;QAAAC,QAAA,GAE1DxC,SAAS,iBACRpC,OAAA;UAAK2E,SAAS,EAAC,+GAA+G;UAAAC,QAAA,gBAC5H5E,OAAA,CAACR,WAAW;YAACmF,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEhF,OAAA;YAAA4E,QAAA,gBACE5E,OAAA;cAAG2E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJhF,OAAA;cAAG2E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAExC;YAAS;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDhF,OAAA;UAAK2E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD5E,OAAA;YAAK2E,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzB5E,OAAA;cACE2E,SAAS,EAAE,oDACTD,SAAS,GAAG,WAAW,GAAG,WAAW,EACpC;cAAAE,QAAA,gBAEH5E,OAAA;gBAAK2E,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C5E,OAAA,CAACV,QAAQ;kBAACqF,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9ChF,OAAA;kBAAI2E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAO2E,SAAS,EAAC,yDAAyD;oBAAAC,QAAA,GAAC,aAC9D,eAAA5E,OAAA;sBAAM2E,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACRhF,OAAA;oBACE4C,IAAI,EAAC,eAAe;oBACpBC,KAAK,EAAEtB,QAAQ,CAACK,aAAc;oBAC9BsD,QAAQ,EAAExC,iBAAkB;oBAC5ByC,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,eAAe,CAAE;oBAChD6C,MAAM,EAAEA,CAAA,KAAM7C,eAAe,CAAC,IAAI,CAAE;oBACpCoC,SAAS,EAAE,oGACTxC,MAAM,CAACP,aAAa,GAChB,qCAAqC,GACrCU,YAAY,KAAK,eAAe,GAChC,oDAAoD,GACpD,4DAA4D,EAC/D;oBAAAsC,QAAA,gBAEH5E,OAAA;sBAAQ6C,KAAK,EAAC,EAAE;sBAAA+B,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1ClE,eAAe,aAAfA,eAAe,wBAAAJ,qBAAA,GAAfI,eAAe,CAAED,IAAI,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuB2E,GAAG,CAAEC,IAAI,iBAC/BtF,OAAA;sBAAsB6C,KAAK,EAAEyC,IAAI,CAACtB,EAAG;sBAAAY,QAAA,EAClCU,IAAI,CAAC1C;oBAAI,GADC0C,IAAI,CAACtB,EAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEZ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,EACR7C,MAAM,CAACP,aAAa,iBACnB5B,OAAA;oBAAG2E,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBACxD5E,OAAA,CAACR,WAAW;sBAACmF,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvC7C,MAAM,CAACP,aAAa;kBAAA;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENhF,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAO2E,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,GAAC,OACnE,eAAA5E,OAAA;sBAAM2E,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACRhF,OAAA;oBACE4C,IAAI,EAAC,SAAS;oBACdC,KAAK,EAAEtB,QAAQ,CAACM,OAAQ;oBACxBqD,QAAQ,EAAExC,iBAAkB;oBAC5ByC,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,SAAS,CAAE;oBAC1C6C,MAAM,EAAEA,CAAA,KAAM7C,eAAe,CAAC,IAAI,CAAE;oBACpCgD,QAAQ,EAAE,CAAChE,QAAQ,CAACK,aAAc;oBAClC+C,SAAS,EAAE,uFACT,CAACpD,QAAQ,CAACK,aAAa,GACnB,gDAAgD,GAChDO,MAAM,CAACN,OAAO,GACd,qCAAqC,GACrCS,YAAY,KAAK,SAAS,GAC1B,iDAAiD,GACjD,gEAAgE,EACnE;oBAAAsC,QAAA,gBAEH5E,OAAA;sBAAQ6C,KAAK,EAAC,EAAE;sBAAA+B,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACpCzD,QAAQ,CAACK,aAAa,KACrBX,SAAS,aAATA,SAAS,wBAAAN,eAAA,GAATM,SAAS,CAAEJ,IAAI,cAAAF,eAAA,wBAAAC,qBAAA,GAAfD,eAAA,CAAiB6E,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC7D,aAAa,KAAK8D,QAAQ,CAACnE,QAAQ,CAACK,aAAa,CAAC,CAAC,cAAAhB,qBAAA,uBAAxFA,qBAAA,CAA0FyE,GAAG,CAAEI,IAAI,iBACjGzF,OAAA;sBAAsB6C,KAAK,EAAE4C,IAAI,CAACzB,EAAG;sBAAAY,QAAA,EAClCa,IAAI,CAAC7C;oBAAI,GADC6C,IAAI,CAACzB,EAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEZ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EACR7C,MAAM,CAACN,OAAO,iBACb7B,OAAA;oBAAG2E,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBACxD5E,OAAA,CAACR,WAAW;sBAACmF,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvC7C,MAAM,CAACN,OAAO;kBAAA;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CACJ,EACA,CAACzD,QAAQ,CAACK,aAAa,iBACtB5B,OAAA;oBAAG2E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,gBACzD5E,OAAA,CAACT,KAAK;sBAACoF,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oCAEpC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhF,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB5E,OAAA;cACE2E,SAAS,EAAE,oDACTD,SAAS,GAAG,WAAW,GAAG,WAAW,EACpC;cAAAE,QAAA,gBAEH5E,OAAA;gBAAK2E,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C5E,OAAA,CAACZ,IAAI;kBAACuF,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1ChF,OAAA;kBAAI2E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C5E,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAO2E,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,GAAC,wBAClD,EAAC,GAAG,eAC1B5E,OAAA;sBAAM2E,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACRhF,OAAA;oBACE2F,IAAI,EAAC,MAAM;oBACX/C,IAAI,EAAC,gBAAgB;oBACrBC,KAAK,EAAEtB,QAAQ,CAACE,cAAe;oBAC/ByD,QAAQ,EAAExC,iBAAkB;oBAC5ByC,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,gBAAgB,CAAE;oBACjD6C,MAAM,EAAEA,CAAA,KAAM7C,eAAe,CAAC,IAAI,CAAE;oBACpCqD,WAAW,EAAC,kCAAkC;oBAC9CjB,SAAS,EAAE,uFACTxC,MAAM,CAACV,cAAc,GACjB,qCAAqC,GACrCa,YAAY,KAAK,gBAAgB,GACjC,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACD7C,MAAM,CAACV,cAAc,iBACpBzB,OAAA;oBAAG2E,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBACxD5E,OAAA,CAACR,WAAW;sBAACmF,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvC7C,MAAM,CAACV,cAAc;kBAAA;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENhF,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAO2E,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,GAAC,WAC/D,eAAA5E,OAAA;sBAAM2E,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACRhF,OAAA;oBACE2F,IAAI,EAAC,MAAM;oBACX/C,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAEtB,QAAQ,CAACG,QAAS;oBACzBwD,QAAQ,EAAExC,iBAAkB;oBAC5ByC,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,UAAU,CAAE;oBAC3C6C,MAAM,EAAEA,CAAA,KAAM7C,eAAe,CAAC,IAAI,CAAE;oBACpCqD,WAAW,EAAC,yBAAyB;oBACrCjB,SAAS,EAAE,uFACTxC,MAAM,CAACT,QAAQ,GACX,qCAAqC,GACrCY,YAAY,KAAK,UAAU,GAC3B,oDAAoD,GACpD,4DAA4D;kBAC/D;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACD7C,MAAM,CAACT,QAAQ,iBACd1B,OAAA;oBAAG2E,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBACxD5E,OAAA,CAACR,WAAW;sBAACmF,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvC7C,MAAM,CAACT,QAAQ;kBAAA;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhF,OAAA;UAAK2E,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5E,OAAA;YAAK2E,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D5E,OAAA;cAAK2E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C5E,OAAA,CAACX,IAAI;gBAACsF,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1ChF,OAAA;gBAAI2E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAENhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAO2E,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,GAAC,WAC/D,eAAA5E,OAAA;kBAAM2E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACRhF,OAAA;gBAAK2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5E,OAAA;kBACE2F,IAAI,EAAEzD,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCU,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEtB,QAAQ,CAACI,QAAS;kBACzBuD,QAAQ,EAAExC,iBAAkB;kBAC5ByC,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,UAAU,CAAE;kBAC3C6C,MAAM,EAAEA,CAAA,KAAM7C,eAAe,CAAC,IAAI,CAAE;kBACpCqD,WAAW,EAAC,gBAAgB;kBAC5BjB,SAAS,EAAE,6FACTxC,MAAM,CAACR,QAAQ,GACX,qCAAqC,GACrCW,YAAY,KAAK,UAAU,GAC3B,oDAAoD,GACpD,gEAAgE;gBACnE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEFhF,OAAA;kBAAK2E,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,gBACpF5E,OAAA;oBACE2F,IAAI,EAAC,QAAQ;oBACbV,OAAO,EAAET,oBAAqB;oBAC9Be,QAAQ,EAAE,CAAClF,iBAAkB;oBAC7BsE,SAAS,EAAE,sGACTtE,iBAAiB,GACb,uDAAuD,GACvD,8CAA8C,EACjD;oBACHyB,KAAK,EAAC,wBAAwB;oBAAA8C,QAAA,gBAE9B5E,OAAA,CAACN,KAAK;sBAACiF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7BhF,OAAA;sBAAA4E,QAAA,EAAM;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAEThF,OAAA;oBACE2F,IAAI,EAAC,QAAQ;oBACbV,OAAO,EAAER,wBAAyB;oBAClCE,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAE9G1C,YAAY,gBACXlC,OAAA,CAACf,MAAM;sBAAC0F,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE9BhF,OAAA,CAAChB,GAAG;sBAAC2F,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC3B;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL7C,MAAM,CAACR,QAAQ,iBACd3B,OAAA;gBAAG2E,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD5E,OAAA,CAACR,WAAW;kBAACmF,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACvC7C,MAAM,CAACR,QAAQ;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACJ,EAGAzD,QAAQ,CAACI,QAAQ,iBAChB3B,OAAA;gBAAK2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B5E,OAAA;kBAAK2E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5E,OAAA;oBAAM2E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAEpD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPhF,OAAA;oBACE2E,SAAS,EAAE,8CAA8CxB,gBAAgB,CACvE5B,QAAQ,CAACkB,QACX,CAAC,EAAG;oBAAAmC,QAAA,EAEHrD,QAAQ,CAACkB;kBAAQ;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNhF,OAAA;kBAAK2E,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClD5E,OAAA;oBACE2E,SAAS,EAAE,gDACTvB,mBAAmB,CAAC7B,QAAQ,CAACkB,QAAQ,CAAC,CAACa,KAAK,EAC3C;oBACHuC,KAAK,EAAE;sBACLxC,KAAK,EAAED,mBAAmB,CAAC7B,QAAQ,CAACkB,QAAQ,CAAC,CAACY;oBAChD;kBAAE;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhF,OAAA;QAAK2E,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAC9F5E,OAAA;UAAK2E,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE5E,OAAA,CAACP,WAAW;YAACkF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnChF,OAAA;YAAA4E,QAAA,EAAM;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5E,OAAA;YACE2F,IAAI,EAAC,QAAQ;YACbV,OAAO,EAAE7E,QAAS;YAClBuE,SAAS,EAAC,0MAA0M;YAAAC,QAAA,EACrN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThF,OAAA;YACE2F,IAAI,EAAC,QAAQ;YACbV,OAAO,EAAErB,YAAa;YACtB2B,QAAQ,EAAEpE,SAAS,IAAIC,UAAU,IAAIE,UAAW;YAChDqD,SAAS,EAAC,gQAAgQ;YAAAC,QAAA,EAExQzD,SAAS,IAAIC,UAAU,IAAIE,UAAU,gBACrCtB,OAAA,CAAAE,SAAA;cAAA0E,QAAA,gBACE5E,OAAA;gBAAK2E,SAAS,EAAC;cAA8E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpGhF,OAAA;gBAAA4E,QAAA,EAAOrE,QAAQ,GAAG,aAAa,GAAG;cAAW;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,eACrD,CAAC,gBAEHhF,OAAA,CAAAE,SAAA;cAAA0E,QAAA,gBACE5E,OAAA,CAACb,MAAM;gBAACwF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BhF,OAAA;gBAAA4E,QAAA,EAAOrE,QAAQ,GAAG,iBAAiB,GAAG;cAAe;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,eAC7D;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvE,EAAA,CAnjBIN,mBAAmB;EAAA,QAQWR,yBAAyB,EAC/BC,mBAAmB,EACYC,gCAAgC,EAChCC,gCAAgC;AAAA;AAAAgG,EAAA,GAXvF3F,mBAAmB;AAqjBzB,eAAeA,mBAAmB;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}