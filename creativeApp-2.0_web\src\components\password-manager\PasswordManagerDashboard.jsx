// import React, { useState } from 'react';
// import AddPasswordCardForm from './AddPasswordCardForm';

// const PasswordManagerDashboard = () => {

//   // Sample data - in real app this would come from API/state management
//   const [passwordCards, setPasswordCards] = useState([
//     {
//       id: 1,
//       title: 'Platform Name',
//       username: '<EMAIL>',
//       password: '••••••••••••',
//       actualPassword: 'xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn',
//       team: 'Team Name',
//       department: 'Department name',
//       strength: 'Weak Password',
//       strengthColor: 'bg-red-100 text-red-600 border-red-300'
//     },
//     {
//       id: 2,
//       title: 'Platform Name',
//       username: '<EMAIL>',
//       password: '••••••••••••',
//       actualPassword: 'StrongPass123!@#',
//       team: 'Team Name',
//       department: 'Department name',
//       strength: 'Strong Password',
//       strengthColor: 'bg-green-100 text-green-600 border-green-300'
//     },
//     {
//       id: 3,
//       title: 'Platform Name',
//       username: '<EMAIL>',
//       password: '••••••••••••',
//       actualPassword: 'ModeratePass456',
//       team: 'Team Name',
//       department: 'Department name',
//       strength: 'Moderate Password',
//       strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'
//     },
//     {
//       id: 4,
//       title: 'Platform Name',
//       username: '<EMAIL>',
//       password: '••••••••••••',
//       actualPassword: 'WeakPass',
//       team: 'Team Name',
//       department: 'Department name',
//       strength: 'Weak Password',
//       strengthColor: 'bg-red-100 text-red-600 border-red-300'
//     },
//     {
//       id: 5,
//       title: 'Platform Name',
//       username: '<EMAIL>',
//       password: '••••••••••••',
//       actualPassword: 'AnotherStrongPass789!',
//       team: 'Team Name',
//       department: 'Department name',
//       strength: 'Strong Password',
//       strengthColor: 'bg-green-100 text-green-600 border-green-300'
//     },
//     {
//       id: 6,
//       title: 'Platform Name',
//       username: '<EMAIL>',
//       password: '••••••••••••',
//       actualPassword: 'ModerateSecure123',
//       team: 'Team Name',
//       department: 'Department name',
//       strength: 'Moderate Password',
//       strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'
//     },
//     {
//       id: 7,
//       title: 'Platform Name',
//       username: '<EMAIL>',
//       password: '••••••••••••',
//       actualPassword: 'VeryWeakPass',
//       team: 'Team Name',
//       department: 'Department name',
//       strength: 'Weak Password',
//       strengthColor: 'bg-red-100 text-red-600 border-red-300'
//     }
//   ]);

//   const [visiblePasswords, setVisiblePasswords] = useState({});
//   const [showTeamTable, setShowTeamTable] = useState(true);
//   const [showAddForm, setShowAddForm] = useState(false);
//   const [showNewTable, setShowNewTable] = useState(false);
//   const [newPasswordCards, setNewPasswordCards] = useState([]);
//   const [editingRowId, setEditingRowId] = useState(null);
//   const [shareableCards, setShareableCards] = useState([]);
//   const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

//   const togglePasswordVisibility = (id) => {
//     setVisiblePasswords(prev => ({
//       ...prev,
//       [id]: !prev[id]
//     }));
//   };

//   const copyToClipboard = (text) => {
//     navigator.clipboard.writeText(text);
//     // You could add a toast notification here
//   };



//   const handleDelete = (id) => {
//     console.log('Delete password card:', id);
//     // Show confirmation dialog and delete
//     setPasswordCards(prev => prev.filter(card => card.id !== id));
//   };

//   // Handle delete entire team table
//   const handleDeleteTeamTable = () => {
//     if (window.confirm('Are you sure you want to delete the entire Team Password Card table?')) {
//       setShowTeamTable(false);
//     }
//   };

//   // Handle shareable toggle
//   const toggleShareable = (id) => {
//     setShareableCards(prev =>
//       prev.includes(id)
//         ? prev.filter(cardId => cardId !== id)
//         : [...prev, id]
//     );
//   };

//   // Handle sorting
//   const handleSort = (key) => {
//     let direction = 'asc';
//     if (sortConfig.key === key && sortConfig.direction === 'asc') {
//       direction = 'desc';
//     }
//     setSortConfig({ key, direction });
//   };

//   // Sort cards based on current sort config
//   const getSortedCards = (cards) => {
//     if (!sortConfig.key) return cards;

//     return [...cards].sort((a, b) => {
//       const aValue = a[sortConfig.key].toLowerCase();
//       const bValue = b[sortConfig.key].toLowerCase();

//       if (sortConfig.direction === 'asc') {
//         return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
//       } else {
//         return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
//       }
//     });
//   };

//   // Handle creating new table
//   const handleCreateNewTable = () => {
//     setShowNewTable(true);
//   };

//   // Handle form submission for new password cards
//   const handleAddPasswordCard = (cardData) => {
//     const newCard = {
//       ...cardData,
//       id: Date.now(),
//       password: '••••••••••••',
//       actualPassword: cardData.password
//     };
//     setNewPasswordCards(prev => [...prev, newCard]);
//     setShowAddForm(false);
//   };

//   // Handle edit for new table cards
//   const handleEditNewCard = (id, field, value) => {
//     setNewPasswordCards(prev => prev.map(card =>
//       card.id === id ? { ...card, [field]: value } : card
//     ));
//   };

//   return (
//     <div className="bg-white dark:bg-gray-900 p-6 rounded-xl">
//       {showTeamTable && (
//         <>
//           {/* Header */}
//           <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6">
//             <div className="flex items-center space-x-4">
//               <h2 className="text-left text-2xl font-bold text-gray-900 dark:text-white">Teams Password Card</h2>

//               {/* Delete Team Table Button */}
//               <button
//                 onClick={handleDeleteTeamTable}
//                 className="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50"
//                 title="Delete entire table"
//               >
//                 <span className="material-symbols-rounded">delete</span>
//               </button>
//             </div>

//             <div className="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
//               <button
//                 onClick={() => setShowAddForm(true)}
//                 className="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
//               >
//                 <span className="material-symbols-rounded mr-2">add</span>
//                 Add Password
//               </button>
//             </div>
//           </div>

//           {/* Table */}
//           <div className="overflow-x-auto">
//             <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
//               <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
//                 <tr>
//                   <th scope="col" className="p-4">Shareable</th>
//                   <th scope="col" className="px-6 py-3">
//                     <div className="flex items-center cursor-pointer" onClick={() => handleSort('title')}>
//                       <span>Title</span>
//                       <div className="ml-2 flex flex-col">
//                         <span className={`text-xs ${sortConfig.key === 'title' && sortConfig.direction === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}>▲</span>
//                         <span className={`text-xs ${sortConfig.key === 'title' && sortConfig.direction === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}>▼</span>
//                       </div>
//                     </div>
//                   </th>
//                   <th scope="col" className="px-6 py-3">
//                     <div className="flex items-center cursor-pointer" onClick={() => handleSort('username')}>
//                       <span>User Name</span>
//                       <div className="ml-2 flex flex-col">
//                         <span className={`text-xs ${sortConfig.key === 'username' && sortConfig.direction === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}>▲</span>
//                         <span className={`text-xs ${sortConfig.key === 'username' && sortConfig.direction === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}>▼</span>
//                       </div>
//                     </div>
//                   </th>
//                   <th scope="col" className="px-6 py-3">Password</th>
//                   <th scope="col" className="px-6 py-3">Team</th>
//                   <th scope="col" className="px-6 py-3">Department</th>
//                   <th scope="col" className="px-6 py-3">Level</th>
//                   <th scope="col" className="px-6 py-3">Action</th>
//                 </tr>
//               </thead>
//               <tbody>
//                 {getSortedCards(passwordCards).map((card) => (
//                   <tr key={card.id} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
//                     <td className="w-4 p-4">
//                       <div className="flex items-center">
//                         <input
//                           id={`shareable-${card.id}`}
//                           type="checkbox"
//                           checked={shareableCards.includes(card.id)}
//                           onChange={() => toggleShareable(card.id)}
//                           className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
//                         />
//                         <label htmlFor={`shareable-${card.id}`} className="sr-only">shareable</label>
//                       </div>
//                     </td>
//                     <td className="px-6 py-4">
//                       <div className="flex items-center">
//                         <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3">
//                           TG
//                         </div>
//                         {editingRowId === card.id ? (
//                           <input
//                             type="text"
//                             defaultValue={card.title}
//                             className="font-medium text-gray-900 border rounded px-2 py-1"
//                             onBlur={(e) => {
//                               // Update card title
//                               setPasswordCards(prev => prev.map(c =>
//                                 c.id === card.id ? {...c, title: e.target.value} : c
//                               ));
//                               setEditingRowId(null);
//                             }}
//                             onKeyDown={(e) => {
//                               if (e.key === 'Enter') {
//                                 e.target.blur();
//                               }
//                             }}
//                             autoFocus
//                           />
//                         ) : (
//                           <span className="font-medium text-gray-900 dark:text-white">{card.title}</span>
//                         )}
//                       </div>
//                     </td>
//                     <td className="px-6 py-4">
//                       <div className="flex items-center">
//                         {editingRowId === card.id ? (
//                           <input
//                             type="text"
//                             defaultValue={card.username}
//                             className="text-gray-900 border rounded px-2 py-1"
//                             onBlur={(e) => {
//                               // Update card username
//                               setPasswordCards(prev => prev.map(c =>
//                                 c.id === card.id ? {...c, username: e.target.value} : c
//                               ));
//                             }}
//                             onKeyDown={(e) => {
//                               if (e.key === 'Enter') {
//                                 e.target.blur();
//                               }
//                             }}
//                           />
//                         ) : (
//                           <span className="text-gray-900 dark:text-white">{card.username}</span>
//                         )}
//                         <button
//                           onClick={() => copyToClipboard(card.username)}
//                           className="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
//                         >
//                           <span className="material-symbols-rounded text-sm">content_copy</span>
//                         </button>
//                       </div>
//                     </td>
//                     <td className="px-6 py-4">
//                       <div className="flex items-center">
//                         <span className="text-gray-900 dark:text-white mr-2">
//                           {visiblePasswords[card.id] ? card.actualPassword : card.password}
//                         </span>
//                         <button
//                           onClick={() => togglePasswordVisibility(card.id)}
//                           className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2"
//                         >
//                           <span className="material-symbols-rounded text-sm">
//                             {visiblePasswords[card.id] ? 'visibility_off' : 'visibility'}
//                           </span>
//                         </button>
//                         <button
//                           onClick={() => copyToClipboard(card.actualPassword)}
//                           className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
//                         >
//                           <span className="material-symbols-rounded text-sm">content_copy</span>
//                         </button>
//                       </div>
//                     </td>
//                     <td className="px-6 py-4 text-gray-900 dark:text-white">{card.team}</td>
//                     <td className="px-6 py-4 text-gray-900 dark:text-white">{card.department}</td>
//                     <td className="px-6 py-4">
//                       <span className={`px-2 py-1 text-xs font-medium rounded-full border ${card.strengthColor}`}>
//                         {card.strength}
//                       </span>
//                     </td>
//                     <td className="px-6 py-4">
//                       <div className="flex items-center space-x-2">
//                         <button
//                           onClick={() => setEditingRowId(editingRowId === card.id ? null : card.id)}
//                           className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
//                           title={editingRowId === card.id ? "Cancel Edit" : "Edit"}
//                         >
//                           <span className="material-symbols-rounded text-sm">
//                             {editingRowId === card.id ? 'close' : 'edit'}
//                           </span>
//                         </button>
//                         <button
//                           onClick={() => handleDelete(card.id)}
//                           className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
//                           title="Delete"
//                         >
//                           <span className="material-symbols-rounded text-sm">delete</span>
//                         </button>
//                       </div>
//                     </td>
//                   </tr>
//                 ))}
//               </tbody>
//             </table>
//           </div>
//         </>
//       )}


//       {/* Add Password Form */}
//       {showAddForm && (
//         <AddPasswordCardForm
//           onSubmit={handleAddPasswordCard}
//           onCancel={() => setShowAddForm(false)}
//         />
//       )}

//       {/* New Password Cards Table */}
//       {showNewTable && (
//         <div className="mt-8">
//           <h3 className="text-xl font-bold text-gray-900 mb-4">New Password Cards</h3>
//           <div className="overflow-x-auto">
//             <table className="w-full text-sm text-left text-gray-500">
//               <thead className="text-xs text-gray-700 uppercase bg-gray-50">
//                 <tr>
//                   <th scope="col" className="p-4">Shareable</th>
//                   <th scope="col" className="px-6 py-3">
//                     <div className="flex items-center cursor-pointer" onClick={() => handleSort('title')}>
//                       <span>Title</span>
//                       <div className="ml-2 flex flex-col">
//                         <span className={`text-xs ${sortConfig.key === 'title' && sortConfig.direction === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}>▲</span>
//                         <span className={`text-xs ${sortConfig.key === 'title' && sortConfig.direction === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}>▼</span>
//                       </div>
//                     </div>
//                   </th>
//                   <th scope="col" className="px-6 py-3">
//                     <div className="flex items-center cursor-pointer" onClick={() => handleSort('username')}>
//                       <span>User Name</span>
//                       <div className="ml-2 flex flex-col">
//                         <span className={`text-xs ${sortConfig.key === 'username' && sortConfig.direction === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}>▲</span>
//                         <span className={`text-xs ${sortConfig.key === 'username' && sortConfig.direction === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}>▼</span>
//                       </div>
//                     </div>
//                   </th>
//                   <th scope="col" className="px-6 py-3">Password</th>
//                   <th scope="col" className="px-6 py-3">Team</th>
//                   <th scope="col" className="px-6 py-3">Department</th>
//                   <th scope="col" className="px-6 py-3">Level</th>
//                   <th scope="col" className="px-6 py-3">Action</th>
//                 </tr>
//               </thead>
//               <tbody>
//                 {newPasswordCards.length === 0 ? (
//                   <tr>
//                     <td colSpan="8" className="px-6 py-8 text-center text-gray-500">
//                       No password cards added yet. Click "Add Password" to add your first card.
//                     </td>
//                   </tr>
//                 ) : (
//                   getSortedCards(newPasswordCards).map((card) => (
//                     <tr key={card.id} className="bg-white border-b hover:bg-gray-50">
//                       <td className="w-4 p-4">
//                         <input
//                           type="checkbox"
//                           checked={shareableCards.includes(card.id)}
//                           onChange={() => toggleShareable(card.id)}
//                           className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
//                         />
//                       </td>
//                       <td className="px-6 py-4">
//                         <div className="flex items-center">
//                           <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3">
//                             TG
//                           </div>
//                           {editingRowId === card.id ? (
//                             <input
//                               type="text"
//                               defaultValue={card.title}
//                               className="font-medium text-gray-900 border rounded px-2 py-1"
//                               onBlur={(e) => {
//                                 handleEditNewCard(card.id, 'title', e.target.value);
//                                 setEditingRowId(null);
//                               }}
//                               onKeyDown={(e) => {
//                                 if (e.key === 'Enter') {
//                                   e.target.blur();
//                                 }
//                               }}
//                               autoFocus
//                             />
//                           ) : (
//                             <span className="font-medium text-gray-900">{card.title}</span>
//                           )}
//                         </div>
//                       </td>
//                       <td className="px-6 py-4">
//                         <div className="flex items-center">
//                           {editingRowId === card.id ? (
//                             <input
//                               type="text"
//                               defaultValue={card.username}
//                               className="text-gray-900 border rounded px-2 py-1"
//                               onBlur={(e) => {
//                                 handleEditNewCard(card.id, 'username', e.target.value);
//                               }}
//                               onKeyDown={(e) => {
//                                 if (e.key === 'Enter') {
//                                   e.target.blur();
//                                 }
//                               }}
//                             />
//                           ) : (
//                             <span className="text-gray-900">{card.username}</span>
//                           )}
//                           <button
//                             onClick={() => copyToClipboard(card.username)}
//                             className="ml-2 text-gray-400 hover:text-gray-600"
//                           >
//                             <span className="material-symbols-rounded text-sm">content_copy</span>
//                           </button>
//                         </div>
//                       </td>
//                       <td className="px-6 py-4">
//                         <div className="flex items-center">
//                           <span className="text-gray-900 mr-2">{card.password}</span>
//                           <button
//                             onClick={() => copyToClipboard(card.actualPassword)}
//                             className="text-gray-400 hover:text-gray-600"
//                           >
//                             <span className="material-symbols-rounded text-sm">content_copy</span>
//                           </button>
//                         </div>
//                       </td>
//                       <td className="px-6 py-4 text-gray-900">{card.team}</td>
//                       <td className="px-6 py-4 text-gray-900">{card.department}</td>
//                       <td className="px-6 py-4">
//                         <span className={`px-2 py-1 text-xs font-medium rounded-full border ${card.strengthColor}`}>
//                           {card.strength}
//                         </span>
//                       </td>
//                       <td className="px-6 py-4">
//                         <div className="flex items-center space-x-2">
//                           <button
//                             onClick={() => setEditingRowId(editingRowId === card.id ? null : card.id)}
//                             className="text-blue-600 hover:text-blue-800"
//                             title={editingRowId === card.id ? "Cancel Edit" : "Edit"}
//                           >
//                             <span className="material-symbols-rounded text-sm">
//                               {editingRowId === card.id ? 'close' : 'edit'}
//                             </span>
//                           </button>
//                           <button
//                             onClick={() => setNewPasswordCards(prev => prev.filter(c => c.id !== card.id))}
//                             className="text-red-600 hover:text-red-800"
//                             title="Delete"
//                           >
//                             <span className="material-symbols-rounded text-sm">delete</span>
//                           </button>
//                         </div>
//                       </td>
//                     </tr>
//                   ))
//                 )}
//               </tbody>
//             </table>
//           </div>
//         </div>
//       )}

//       <div className="flex justify-center mt-6">
//         <button
//           onClick={handleCreateNewTable}
//           className="flex items-center justify-center px-6 py-2 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 focus:outline-none"
//         >
//           <span className="material-symbols-rounded mr-2">add</span>
//           Add New Password Card
//         </button>
//       </div>
//     </div>
//   );
// };

// export default PasswordManagerDashboard;