import { baseApi } from './baseApi';
import { alertMessage } from '../../common/coreui';

export const passwordManagerApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPasswordManagerData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `password-managers?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        return queryString;
      },
      providesTags: ['PasswordManagerData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),



    getPasswordManagerById: builder.query({
      query: (id) => `password-managers/${id}`,
      providesTags: (result, error, id) => [{ type: 'PasswordManagerData', id }],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    getDecryptedPassword: builder.query({
      query: (id) => `password-managers/${id}/password`,
      providesTags: (result, error, id) => [{ type: 'DecryptedPassword', id }],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createPasswordManager: builder.mutation({
      query: (data) => ({
        url: 'password-managers',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['PasswordManagerData'],
      // FIXED: Removed duplicate notification - handled in component
    }),

    updatePasswordManager: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `password-managers/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['PasswordManagerData'],
      // FIXED: Removed duplicate notification - handled in component
    }),

    deletePasswordManager: builder.mutation({
      query: (id) => ({
        url: `password-managers/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['PasswordManagerData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetPasswordManagerDataQuery,
  useGetPasswordManagerByIdQuery,
  useLazyGetPasswordManagerByIdQuery,
  useLazyGetDecryptedPasswordQuery,
  useCreatePasswordManagerMutation,
  useUpdatePasswordManagerMutation,
  useDeletePasswordManagerMutation,
} = passwordManagerApi;
