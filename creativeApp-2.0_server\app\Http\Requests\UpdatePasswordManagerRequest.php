<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdatePasswordManagerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization handled in controller
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        // Get the password manager ID from the route parameter
        $passwordManager = $this->route('password_manager');
        $passwordManagerId = $passwordManager ? $passwordManager->id : null;

        return [
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'user_id' => 'nullable|exists:users,id',
            'password_title' => 'required|string|max:255',
            'username' => 'required|string|max:255',
            'user_name' => "sometimes|string|max:255|unique:password_managers,user_name,{$passwordManagerId}",
            'password' => 'sometimes|string|min:1|max:1000',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set user_name for backward compatibility (use password_title as the primary identifier)
        $userNameValue = $this->input('password_title') ?: $this->input('username');

        $this->merge([
            'user_name' => $userNameValue
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'department_id.exists' => 'The selected department does not exist.',
            'team_id.exists' => 'The selected team does not exist.',
            'user_id.exists' => 'The selected user does not exist.',
            'password_title.required' => 'Platform/Service title is required.',
            'password_title.max' => 'Platform/Service title may not be greater than 255 characters.',
            'password_title.unique' => 'This platform/service title already exists for this user.',
            'password.min' => 'Password must be at least 1 character.',
            'password.max' => 'Password may not be greater than 1000 characters.',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param Validator $validator
     * @return void
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422)
        );
    }
}
