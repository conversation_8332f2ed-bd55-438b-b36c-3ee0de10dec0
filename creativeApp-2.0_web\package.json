{"name": "creativeapp", "version": "0.1.0", "private": true, "dependencies": {"@floating-ui/react": "^0.27.4", "@fontsource/roboto": "^5.1.0", "@headlessui/react": "^2.2.0", "@material-tailwind/react": "^2.1.10", "@react-spring/web": "latest", "@reduxjs/toolkit": "^2.6.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/react": "latest", "@types/react-dom": "latest", "@umalqura/core": "^0.0.7", "axios": "^1.7.7", "clsx": "latest", "date-bengali-revised": "^2.0.2", "date-fns": "^4.1.0", "dayjs": "latest", "dompurify": "^3.2.3", "dotenv": "^17.0.1", "file-saver": "^2.0.5", "flowbite": "^2.5.2", "google-icon": "^1.0.1", "headlessui": "^0.0.0", "html-react-parser": "^5.2.2", "lucide-react": "^0.525.0", "material-react-table": "^3.0.1", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "openmeteo": "^1.1.4", "react": "^18.3.1", "react-big-calendar": "^1.17.1", "react-data-table-component": "^7.6.2", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-multi-date-picker": "^4.5.2", "react-quill": "^2.0.0", "react-quilljs": "^2.0.5", "react-redux": "^9.2.0", "react-router-dom": "^6.27.0", "react-scripts": "5.0.1", "react-select": "^5.9.0", "react-tailwindcss-datepicker": "^1.7.2", "react-time-picker": "^7.0.0", "sonner": "^2.0.6", "styled-components": "^6.1.13", "sweetalert2": "^11.15.10", "tailgrids": "^2.2.7", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "tailwindcss": "^3.4.14"}}