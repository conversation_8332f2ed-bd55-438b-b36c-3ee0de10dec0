import React, { useState } from "react";
import AddPasswordCardForm from "./AddPasswordCardForm";
import { confirmationAlert } from "../../common/coreui";
import FetchLoggedInRole from "../../common/fetchData/FetchLoggedInRole";
import {
  useGetPasswordManagerDataQuery,
  useDeletePasswordManagerMutation,
  useUpdatePasswordManagerMutation
} from "../../features/api/passwordManagerApi";
import { toast } from "sonner";

const PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {
  // Get current user data
  const { userData } = FetchLoggedInRole();
  const currentUserId = userData?.id;

  // API hooks
  const { data: dataItems, isLoading, refetch } = useGetPasswordManagerDataQuery({
    sort_by: "created_at",
    order: "desc",
    page: 1,
    per_page: 100,
    query: "",
  });

  const [deletePasswordManager] = useDeletePasswordManagerMutation();
  const [updatePasswordManager] = useUpdatePasswordManagerMutation();

  // State for managing multiple tables
  const [tables, setTables] = useState([
    { id: 1, name: "Teams Password Card" }
  ]);

  // State for each table
  const [tableStates, setTableStates] = useState({
    1: {
      showAddForm: false,
      editingRowId: null,
      visiblePasswords: {},
      shareableCards: [],
      editingHeader: false,
      headerTitle: "Teams Password Card"
    }
  });

  // Get state for specific table
  const getTableState = (tableId) => {
    return tableStates[tableId] || {
      showAddForm: false,
      editingRowId: null,
      visiblePasswords: {},
      shareableCards: [],
      editingHeader: false,
      headerTitle: `Teams Password Card ${tableId}`
    };
  };

  // Update state for specific table
  const updateTableState = (tableId, updates) => {
    setTableStates(prev => ({
      ...prev,
      [tableId]: {
        ...getTableState(tableId),
        ...updates
      }
    }));
  };

  // Add new table
  const handleAddNewTable = () => {
    const newTableId = Math.max(...tables.map(t => t.id)) + 1;
    const newTable = {
      id: newTableId,
      name: `Teams Password Card ${newTableId}`
    };
    setTables(prev => [...prev, newTable]);

    // Initialize state for new table
    setTableStates(prev => ({
      ...prev,
      [newTableId]: {
        showAddForm: false,
        editingRowId: null,
        visiblePasswords: {},
        shareableCards: [],
        editingHeader: false,
        headerTitle: `Teams Password Card ${newTableId}`
      }
    }));

    toast.success("New password table created!");
  };

  // Delete table
  const handleDeleteTable = (tableId) => {
    if (tables.length === 1) {
      toast.error("Cannot delete the last table. At least one table must remain.");
      return;
    }

    setTables(prev => prev.filter(table => table.id !== tableId));

    // Remove state for deleted table
    setTableStates(prev => {
      const newState = { ...prev };
      delete newState[tableId];
      return newState;
    });

    toast.success("Password table deleted!");
  };

  // Toggle password visibility for specific table
  const togglePasswordVisibility = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    updateTableState(tableId, {
      visiblePasswords: {
        ...currentState.visiblePasswords,
        [cardId]: !currentState.visiblePasswords[cardId]
      }
    });
  };

  // Handle edit for specific table
  const handleEdit = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    if (currentState.editingRowId === cardId) {
      // Exit edit mode
      updateTableState(tableId, { editingRowId: null });
    } else {
      // Enter edit mode
      updateTableState(tableId, { editingRowId: cardId });
    }
  };

  // Handle inline edit save
  const handleInlineEditSave = async (tableId, cardId, field, value) => {
    try {
      const item = dataItems?.data?.find(item => item.id === cardId);
      if (!item) return;

      let updateData = {
        password_title: item.password_title || item.title,
        username: item.username,
        password: item.password,
        department_id: item.department_id,
        team_id: item.team_id,
      };

      if (field === 'title') {
        updateData.password_title = value;
      } else if (field === 'username') {
        updateData.username = value;
      }

      await updatePasswordManager({ id: cardId, ...updateData }).unwrap();
      refetch();
      toast.success("Updated successfully!");
    } catch (error) {
      console.error("Error updating:", error);
      toast.error("Failed to update. Please try again.");
    }
  };

  // Handle delete
  const handleDelete = async (cardId) => {
    confirmationAlert({
      onConfirm: async () => {
        try {
          await deletePasswordManager(cardId).unwrap();
          refetch();
          toast.success("Password deleted successfully!");
        } catch (error) {
          console.error("Error deleting password:", error);
          toast.error("Failed to delete password. Please try again.");
        }
      },
    });
  };

  // Handle successful password creation/update
  const handlePasswordSuccess = (tableId) => {
    refetch();
    updateTableState(tableId, {
      showAddForm: false,
      editingRowId: null
    });
    toast.success("Password saved successfully!");
  };

  // Toggle shareable card selection
  const toggleShareableCard = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    const currentShareable = currentState.shareableCards;
    const newShareable = currentShareable.includes(cardId)
      ? currentShareable.filter(id => id !== cardId)
      : [...currentShareable, cardId];

    updateTableState(tableId, { shareableCards: newShareable });
  };

  // Toggle all shareable cards
  const toggleAllShareable = (tableId) => {
    const currentState = getTableState(tableId);
    const tableData = dataItems?.data || [];

    if (currentState.shareableCards.length === tableData.length) {
      updateTableState(tableId, { shareableCards: [] });
    } else {
      updateTableState(tableId, { shareableCards: tableData.map(card => card.id) });
    }
  };

  // Handle share
  const handleShare = async (tableId) => {
    try {
      const currentState = getTableState(tableId);
      const tableData = dataItems?.data || [];
      const selectedCards = tableData.filter(card => currentState.shareableCards.includes(card.id));

      if (selectedCards.length === 0) {
        toast.error("Please select at least one password to share.");
        return;
      }

      const currentUserTeam = userData?.teams?.[0]?.name || "your team";
      toast.success(`Successfully shared ${selectedCards.length} password(s) with ${currentUserTeam} members.`);
      updateTableState(tableId, { shareableCards: [] });

    } catch (error) {
      console.error("Error sharing passwords:", error);
      toast.error("Failed to share passwords. Please try again.");
    }
  };

  // Handle delete entire table entries
  const handleDeleteTableEntries = async (tableId) => {
    const currentState = getTableState(tableId);

    if (currentState.shareableCards.length === 0) {
      toast.error("Please select at least one row to delete.");
      return;
    }

    const tableData = dataItems?.data || [];
    const userOwnedCards = currentState.shareableCards.filter(cardId => {
      const card = tableData.find(c => c.id === cardId);
      return card && card.user_id === currentUserId;
    });

    if (userOwnedCards.length === 0) {
      toast.error("You can only delete password entries that you created.");
      return;
    }

    confirmationAlert({
      onConfirm: async () => {
        try {
          await Promise.all(
            userOwnedCards.map(cardId => deletePasswordManager(cardId).unwrap())
          );

          updateTableState(tableId, { shareableCards: [] });
          refetch();
          toast.success(`Successfully deleted ${userOwnedCards.length} password entries.`);
        } catch (error) {
          console.error("Error deleting password entries:", error);
          toast.error("Failed to delete some password entries. Please try again.");
        }
      },
    });
  };

  // Get team members for avatar display
  const getTeamMembers = () => {
    // Return current user and some placeholder team members
    const members = [userData];
    // Add some placeholder members (in real app, get from API)
    if (userData?.teams?.[0]) {
      // Add placeholder team members
      for (let i = 1; i < 4; i++) {
        members.push({
          id: i,
          fname: `User${i}`,
          lname: `Team`,
          photo: null
        });
      }
    }
    return members.slice(0, 3); // Show max 3 avatars
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const tableData = dataItems?.data || [];

  return (
    <div className="bg-white dark:bg-gray-900">
      {/* Add New Table Button */}
      <div className="flex justify-end mb-4">
        <button
          onClick={handleAddNewTable}
          className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
        >
          <span className="material-symbols-outlined mr-2">add</span>
          Add New Table
        </button>
      </div>

      {/* Render all tables */}
      {tables.map((table) => {
        const tableState = getTableState(table.id);
        const teamMembers = getTeamMembers();
        const hasMoreMembers = teamMembers.length > 3;

        return (
          <div key={table.id} className="mb-8">
            {/* Table Header */}
            <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6">
              <div className="flex items-center space-x-4">
                {/* Team Member Avatars */}
                <div className="flex items-center space-x-2">
                  {teamMembers.map((member, index) => (
                    <div key={index} className="relative">
                      {member?.photo ? (
                        <img
                          src={member.photo}
                          alt={`${member.fname} ${member.lname}`}
                          className="w-8 h-8 rounded-full object-cover border-2 border-gray-200"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {member?.fname?.charAt(0)}{member?.lname?.charAt(0)}
                        </div>
                      )}
                    </div>
                  ))}
                  {hasMoreMembers && (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-sm font-medium">
                      +
                    </div>
                  )}
                </div>

                {/* Table Title */}
                {tableState.editingHeader ? (
                  <input
                    type="text"
                    value={tableState.headerTitle}
                    onChange={(e) => updateTableState(table.id, { headerTitle: e.target.value })}
                    onBlur={() => updateTableState(table.id, { editingHeader: false })}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        updateTableState(table.id, { editingHeader: false });
                      }
                    }}
                    className="text-left text-xl font-bold text-gray-900 dark:text-white bg-transparent border-b-2 border-primary focus:outline-none focus:border-primary-600"
                    autoFocus
                  />
                ) : (
                  <h2
                    className="text-left text-xl font-bold text-gray-900 dark:text-white cursor-pointer hover:text-primary transition-colors"
                    onClick={() => updateTableState(table.id, { editingHeader: true })}
                    title="Click to edit"
                  >
                    {tableState.headerTitle}
                  </h2>
                )}

                {/* Action Icons */}
                <div className="flex items-center space-x-2">
                  {/* Share Icon */}
                  <button
                    onClick={() => handleShare(table.id)}
                    className="flex items-center justify-center w-6 h-5 text-gray-500 hover:text-primary-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                    title="Share with team"
                  >
                    <span className="material-symbols-rounded">share</span>
                  </button>

                  {/* Delete Table Icon */}
                  <button
                    onClick={() => handleDeleteTableEntries(table.id)}
                    className="flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg"
                    title="Delete selected entries"
                  >
                    <span className="material-symbols-outlined text-sm">delete</span>
                  </button>

                  {/* Delete Entire Table */}
                  <button
                    onClick={() => handleDeleteTable(table.id)}
                    className="flex items-center justify-center w-5 h-5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg"
                    title="Delete entire table"
                  >
                    <span className="material-symbols-outlined text-sm">delete_forever</span>
                  </button>
                </div>
              </div>

              {/* Add Password Button */}
              <button
                onClick={() => {
                  updateTableState(table.id, {
                    editingRowId: null,
                    showAddForm: !tableState.showAddForm
                  });
                }}
                className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium whitespace-nowrap flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out ${
                  tableState.showAddForm
                    ? "bg-primary text-white"
                    : "hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                }`}
              >
                <span className="material-symbols-outlined mr-2">add</span>
                Add Password
              </button>
            </div>

            {/* Add Password Form */}
            {tableState.showAddForm && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <AddPasswordCardForm
                  onCancel={() => {
                    updateTableState(table.id, {
                      showAddForm: false,
                      editingRowId: null
                    });
                  }}
                  onSuccess={() => handlePasswordSuccess(table.id)}
                  generatedPassword={generatedPassword}
                  passwordStrength={passwordStrength}
                  editData={tableState.editingRowId ? tableData.find(item => item.id === tableState.editingRowId) : null}
                />
              </div>
            )}

            {/* Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="overflow-x-auto">
                {tableData.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <span className="material-symbols-outlined text-6xl">lock</span>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No password cards added yet</h3>
                    <p className="text-gray-500 mb-4">Click 'Add Password' to add your first password card.</p>
                  </div>
                ) : (
                  <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                      <tr>
                        <th scope="col" className="px-6 py-3">
                          <input
                            type="checkbox"
                            checked={tableState.shareableCards.length === tableData.length && tableData.length > 0}
                            onChange={() => toggleAllShareable(table.id)}
                            className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary dark:focus:ring-primary dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                          />
                        </th>
                        <th scope="col" className="px-6 py-3">
                          <div className="flex items-center">
                            Platform/Service
                            <button className="ml-1">
                              <span className="material-symbols-outlined text-xs">arrow_upward</span>
                            </button>
                          </div>
                        </th>
                        <th scope="col" className="px-6 py-3">
                          <div className="flex items-center">
                            User Name
                            <button className="ml-1">
                              <span className="material-symbols-outlined text-xs">arrow_upward</span>
                            </button>
                          </div>
                        </th>
                        <th scope="col" className="px-6 py-3">Password</th>
                        <th scope="col" className="px-6 py-3">Team</th>
                        <th scope="col" className="px-6 py-3">Department</th>
                        <th scope="col" className="px-6 py-3">Level</th>
                        <th scope="col" className="px-6 py-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tableData.map((card) => (
                        <TableRow
                          key={card.id}
                          card={card}
                          tableId={table.id}
                          isEditing={tableState.editingRowId === card.id}
                          isShareable={tableState.shareableCards.includes(card.id)}
                          visiblePassword={tableState.visiblePasswords[card.id]}
                          currentUserId={currentUserId}
                          onEdit={handleEdit}
                          onDelete={handleDelete}
                          onTogglePassword={togglePasswordVisibility}
                          onToggleShareable={toggleShareableCard}
                          onInlineEditSave={handleInlineEditSave}
                        />
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

// TableRow component for individual table rows
const TableRow = ({
  card,
  tableId,
  isEditing,
  isShareable,
  visiblePassword,
  currentUserId,
  onEdit,
  onDelete,
  onTogglePassword,
  onToggleShareable,
  onInlineEditSave
}) => {
  const [editValues, setEditValues] = useState({
    title: card.title || "",
    username: card.username || "",
  });

  const handleInputChange = (field, value) => {
    setEditValues(prev => ({ ...prev, [field]: value }));
  };

  const handleInputBlur = (field) => {
    const originalValue = field === 'title' ? card.title : card.username;
    if (editValues[field] !== originalValue) {
      onInlineEditSave(tableId, card.id, field, editValues[field]);
    }
  };

  const handleKeyDown = (e, field) => {
    if (e.key === 'Enter') {
      e.target.blur();
    }
  };

  return (
    <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
      <td className="px-6 py-4">
        <input
          type="checkbox"
          checked={isShareable}
          onChange={() => onToggleShareable(tableId, card.id)}
          className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary dark:focus:ring-primary dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
        />
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium mr-3">
            {card.title?.charAt(0)?.toUpperCase() || 'P'}
          </div>
          {isEditing ? (
            <input
              type="text"
              value={editValues.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              onBlur={() => handleInputBlur('title')}
              onKeyDown={(e) => handleKeyDown(e, 'title')}
              className="font-medium text-gray-900 dark:text-white border rounded px-2 py-1 w-full"
              autoFocus
            />
          ) : (
            <span className="font-medium text-gray-900 dark:text-white">{card.title}</span>
          )}
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center">
          {isEditing ? (
            <input
              type="text"
              value={editValues.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              onBlur={() => handleInputBlur('username')}
              onKeyDown={(e) => handleKeyDown(e, 'username')}
              className="text-gray-900 dark:text-white border rounded px-2 py-1 w-full mr-2"
            />
          ) : (
            <span className="text-gray-900 dark:text-white mr-2">{card.username}</span>
          )}
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center">
          <span className="text-gray-900 dark:text-white mr-2">
            {visiblePassword ? card.password : "••••••••••••"}
          </span>
          <button
            onClick={() => onTogglePassword(tableId, card.id)}
            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
          >
            <span className="material-symbols-outlined text-sm">
              {visiblePassword ? "visibility_off" : "visibility"}
            </span>
          </button>
        </div>
      </td>
      <td className="px-6 py-4">
        <span className="text-gray-900 dark:text-white">{card.team?.name || 'N/A'}</span>
      </td>
      <td className="px-6 py-4">
        <span className="text-gray-900 dark:text-white">{card.department?.name || 'N/A'}</span>
      </td>
      <td className="px-6 py-4">
        <span
          className={`px-3 py-1 text-xs font-medium rounded-full ${
            card.level === "Strong"
              ? "bg-green-100 text-green-600 border border-green-300"
              : card.level === "Moderate"
              ? "bg-yellow-100 text-yellow-600 border border-yellow-300"
              : "bg-red-100 text-red-600 border border-red-300"
          }`}
        >
          {card.level || 'Unknown'}
        </span>
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center space-x-1">
          <button
            onClick={() => onEdit(tableId, card.id)}
            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 dark:text-primary-400 dark:hover:bg-primary dark:hover:text-white dark:focus:ring-gray-700"
            title="Edit"
          >
            <span className="material-symbols-outlined text-lg">edit</span>
          </button>
          {card.user_id === currentUserId && (
            <button
              onClick={() => onDelete(card.id)}
              className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-600 hover:bg-red-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 dark:text-red-400 dark:hover:bg-red-600 dark:hover:text-white dark:focus:ring-gray-700"
              title="Delete"
            >
              <span className="material-symbols-outlined text-lg">delete</span>
            </button>
          )}
        </div>
      </td>
    </tr>
  );
};

export default PasswordCardsTable;