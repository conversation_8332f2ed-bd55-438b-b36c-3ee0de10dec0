import React, { useState, useEffect } from "react";
import AddPassword<PERSON>ardForm from "./AddPasswordCardForm";
import { confirmationAlert } from "../../common/coreui";
import FetchLoggedInRole from "../../common/fetchData/FetchLoggedInRole";
import {
  useGetPasswordManagerDataQuery,
  useDeletePasswordManagerMutation,
  useUpdatePasswordManagerMutation,
} from "../../features/api/passwordManagerApi";
import { toast } from "sonner";

const PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {
  // Get current user data
  const { userData } = FetchLoggedInRole();
  const currentUserId = userData?.id;

  // API hooks
  const {
    data: dataItems,
    isLoading,
    refetch,
  } = useGetPasswordManagerDataQuery({
    sort_by: "created_at",
    order: "desc",
    page: 1,
    per_page: 100,
    query: "",
  });

  const [deletePasswordManager] = useDeletePasswordManagerMutation();
  const [updatePasswordManager] = useUpdatePasswordManagerMutation();

  // State for managing multiple tables
  const [tables, setTables] = useState([
    { id: 1, name: "Teams Password Card" },
  ]);

  // State for each table
  const [tableStates, setTableStates] = useState({
    1: {
      showAddForm: false,
      editingRowId: null,
      visiblePasswords: {},
      shareableCards: [],
      editingHeader: false,
      headerTitle: "Teams Password Card",
    },
  });

  // State to track which passwords belong to which table
  const [tablePasswordAssociations, setTablePasswordAssociations] = useState(
    {}
  );
  const [initializedExistingPasswords, setInitializedExistingPasswords] =
    useState(false);

  // Initialize existing passwords to the first table
  useEffect(() => {
    if (
      !initializedExistingPasswords &&
      dataItems?.data &&
      dataItems.data.length > 0
    ) {
      const existingPasswordIds = dataItems.data.map((item) => item.id);
      setTablePasswordAssociations((prev) => ({
        ...prev,
        1: existingPasswordIds,
      }));
      setInitializedExistingPasswords(true);
    }
  }, [dataItems, initializedExistingPasswords]);

  // Get state for specific table
  const getTableState = (tableId) => {
    return (
      tableStates[tableId] || {
        showAddForm: false,
        editingRowId: null,
        visiblePasswords: {},
        shareableCards: [],
        editingHeader: false,
        headerTitle: `Teams Password Card ${tableId}`,
      }
    );
  };

  // Update state for specific table
  const updateTableState = (tableId, updates) => {
    setTableStates((prev) => ({
      ...prev,
      [tableId]: {
        ...getTableState(tableId),
        ...updates,
      },
    }));
  };

  // Get passwords for a specific table
  const getTableData = (tableId) => {
    const allData = dataItems?.data || [];
    const tablePasswords = tablePasswordAssociations[tableId] || [];
    return allData.filter((item) => tablePasswords.includes(item.id));
  };

  // Associate a password with a table
  const associatePasswordWithTable = (tableId, passwordId) => {
    setTablePasswordAssociations((prev) => ({
      ...prev,
      [tableId]: [...(prev[tableId] || []), passwordId],
    }));
  };

  // Remove password association from table
  const removePasswordFromTable = (tableId, passwordId) => {
    setTablePasswordAssociations((prev) => ({
      ...prev,
      [tableId]: (prev[tableId] || []).filter((id) => id !== passwordId),
    }));
  };

  // Delete table
  const handleDeleteTable = (tableId) => {
    if (tables.length === 1) {
      toast.error(
        "Cannot delete the last table. At least one table must remain."
      );
      return;
    }

    setTables((prev) => prev.filter((table) => table.id !== tableId));

    // Remove state for deleted table
    setTableStates((prev) => {
      const newState = { ...prev };
      delete newState[tableId];
      return newState;
    });

    toast.success("Password table deleted!");
  };

  // Get team members for avatar display - FIXED: Proper backend integration
  const getTeamMembers = () => {
    const members = [];

    // Add current user first
    if (userData) {
      members.push({
        id: userData.id,
        fname: userData.fname || "User",
        lname: userData.lname || "",
        photo: userData.photo || null,
      });
    }

    // FIXED: Add team members from backend data with proper structure
    if (userData?.teams && userData.teams.length > 0) {
      userData.teams.forEach((team) => {
        // Check if team has users (members) loaded
        if (team.users && Array.isArray(team.users)) {
          team.users.forEach((member) => {
            // Avoid duplicate current user
            if (member.id !== userData.id) {
              members.push({
                id: member.id,
                fname: member.fname || "Team",
                lname: member.lname || "Member",
                photo: member.photo || null,
              });
            }
          });
        }
      });
    }

    // If no team members found, add some placeholder members for demo
    if (members.length === 1) {
      for (let i = 2; i <= 4; i++) {
        members.push({
          id: `placeholder_${i}`,
          fname: `Team`,
          lname: `Member ${i}`,
          photo: null,
        });
      }
    }

    return members.slice(0, 4); // Show max 4 avatars
  };

  // Toggle password visibility for specific table
  const togglePasswordVisibility = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    updateTableState(tableId, {
      visiblePasswords: {
        ...currentState.visiblePasswords,
        [cardId]: !currentState.visiblePasswords[cardId],
      },
    });
  };

  // Handle edit for specific table
  const handleEdit = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    if (currentState.editingRowId === cardId) {
      // Exit edit mode
      updateTableState(tableId, { editingRowId: null });
    } else {
      // Enter edit mode
      updateTableState(tableId, { editingRowId: cardId });
    }
  };

  // Handle inline edit save - FIXED: Proper backend integration
  const handleInlineEditSave = async (tableId, cardId, field, value) => {
    try {
      const item = dataItems?.data?.find((item) => item.id === cardId);
      if (!item) {
        toast.error("Item not found");
        return;
      }

      // FIXED: Proper data structure for backend
      let updateData = {
        password_title: item.password_title || item.title || item.user_name,
        username: value, // Always use the new value as username
        password: item.password,
        department_id: item.department_id,
        team_id: item.team_id,
      };

      // FIXED: Handle different field updates properly
      if (field === "title") {
        updateData.password_title = value;
        updateData.username = item.username; // Keep existing username
      } else if (field === "username") {
        updateData.username = value;
        updateData.password_title =
          item.password_title || item.title || item.user_name; // Keep existing title
      } else if (field === "team") {
        updateData.team_id = value;
        updateData.username = item.username;
        updateData.password_title =
          item.password_title || item.title || item.user_name;
      } else if (field === "department") {
        updateData.department_id = value;
        updateData.username = item.username;
        updateData.password_title =
          item.password_title || item.title || item.user_name;
      }

      console.log("Updating with data:", updateData); // Debug log

      await updatePasswordManager({ id: cardId, ...updateData }).unwrap();
      refetch();
      // FIXED: Only show one success notification
      toast.success("Successfully updated!");
    } catch (error) {
      console.error("Error updating:", error);
      toast.error(
        error?.data?.message || "Failed to update. Please try again."
      );
    }
  };

  // Handle delete
  const handleDelete = async (cardId) => {
    confirmationAlert({
      onConfirm: async () => {
        try {
          await deletePasswordManager(cardId).unwrap();

          // Remove from all table associations
          Object.keys(tablePasswordAssociations).forEach((tableId) => {
            removePasswordFromTable(parseInt(tableId), cardId);
          });

          refetch();
          toast.success("Password deleted successfully!");
        } catch (error) {
          console.error("Error deleting password:", error);
          toast.error("Failed to delete password. Please try again.");
        }
      },
    });
  };

  // Handle successful password creation/update - FIXED: Reset form properly
  const handlePasswordSuccess = (tableId, newPasswordId = null) => {
    refetch().then(() => {
      // If a new password was created, associate it with the table
      if (newPasswordId) {
        associatePasswordWithTable(tableId, newPasswordId);
      }
    });
    updateTableState(tableId, {
      showAddForm: false,
      editingRowId: null,
    });
    // FIXED: Only one success notification, no duplicate
    toast.success("Password saved successfully!");
  };

  // Toggle shareable card selection
  const toggleShareableCard = (tableId, cardId) => {
    const currentState = getTableState(tableId);
    const currentShareable = currentState.shareableCards;
    const newShareable = currentShareable.includes(cardId)
      ? currentShareable.filter((id) => id !== cardId)
      : [...currentShareable, cardId];

    updateTableState(tableId, { shareableCards: newShareable });
  };

  // Toggle all shareable cards
  const toggleAllShareable = (tableId) => {
    const currentState = getTableState(tableId);
    const tableData = dataItems?.data || [];

    if (currentState.shareableCards.length === tableData.length) {
      updateTableState(tableId, { shareableCards: [] });
    } else {
      updateTableState(tableId, {
        shareableCards: tableData.map((card) => card.id),
      });
    }
  };

  // Handle share
  const handleShare = async (tableId) => {
    try {
      const currentState = getTableState(tableId);
      const tableData = dataItems?.data || [];
      const selectedCards = tableData.filter((card) =>
        currentState.shareableCards.includes(card.id)
      );

      if (selectedCards.length === 0) {
        toast.error("Please select at least one password to share.");
        return;
      }

      const currentUserTeam = userData?.teams?.[0]?.name || "your team";
      toast.success(
        `Successfully shared ${selectedCards.length} password(s) with ${currentUserTeam} members.`
      );
      updateTableState(tableId, { shareableCards: [] });
    } catch (error) {
      console.error("Error sharing passwords:", error);
      toast.error("Failed to share passwords. Please try again.");
    }
  };

  // Handle delete entire table entries
  const handleDeleteTableEntries = async (tableId) => {
    const currentState = getTableState(tableId);

    if (currentState.shareableCards.length === 0) {
      toast.error("Please select at least one row to delete.");
      return;
    }

    const tableData = getTableData(tableId);
    const userOwnedCards = currentState.shareableCards.filter((cardId) => {
      const card = tableData.find((c) => c.id === cardId);
      return card && card.user_id === currentUserId;
    });

    if (userOwnedCards.length === 0) {
      toast.error("You can only delete password entries that you created.");
      return;
    }

    confirmationAlert({
      onConfirm: async () => {
        try {
          await Promise.all(
            userOwnedCards.map((cardId) =>
              deletePasswordManager(cardId).unwrap()
            )
          );

          // Remove from table associations
          userOwnedCards.forEach((cardId) => {
            removePasswordFromTable(tableId, cardId);
          });

          updateTableState(tableId, { shareableCards: [] });
          refetch();
          toast.success(
            `Successfully deleted ${userOwnedCards.length} password entries from this table.`
          );
        } catch (error) {
          console.error("Error deleting password entries:", error);
          toast.error(
            "Failed to delete some password entries. Please try again."
          );
        }
      },
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900">
      {/* Render all tables */}
      {tables.map((table) => {
        const tableState = getTableState(table.id);
        const teamMembers = getTeamMembers();
        const tableData = getTableData(table.id);

        return (
          <div key={table.id} className="mb-8">
            {/* Table Header - Matching Member Index Design */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                {/* Editable Table Title */}
                {tableState.editingHeader ? (
                  <input
                    type="text"
                    value={tableState.headerTitle}
                    onChange={(e) =>
                      updateTableState(table.id, {
                        headerTitle: e.target.value,
                      })
                    }
                    onBlur={() =>
                      updateTableState(table.id, { editingHeader: false })
                    }
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        updateTableState(table.id, { editingHeader: false });
                      }
                    }}
                    className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                    autoFocus
                  />
                ) : (
                  <h2
                    className="text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                    onClick={() =>
                      updateTableState(table.id, { editingHeader: true })
                    }
                    title="Click to edit table name"
                  >
                    {tableState.headerTitle}
                  </h2>
                )}

                {/* Share Icon */}
                <button
                  onClick={() => handleShare(table.id)}
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  title="Share with team"
                >
                  <span className="material-symbols-outlined text-sm ">
                    share
                  </span>
                </button>

                {/* Delete Selected Icon */}
                <button
                  onClick={() => handleDeleteTableEntries(table.id)}
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  title="Delete selected entries"
                >
                  <span className="material-symbols-outlined text-sm ">
                    delete
                  </span>
                </button>

                {/* Team Member Avatars - Placeholder */}
                <div className="flex items-center -space-x-2 ml-2">
                  {teamMembers.slice(0, 3).map((member, index) => (
                    <div key={index} className="relative">
                      {member?.photo ? (
                        <img
                          src={member.photo}
                          alt={`${member.fname} ${member.lname}`}
                          className="w-8 h-8 rounded-full object-cover border-2 border-white shadow-sm"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm">
                          {member?.fname?.charAt(0)}
                          {member?.lname?.charAt(0)}
                        </div>
                      )}
                    </div>
                  ))}
                  {teamMembers.length > 3 && (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white shadow-sm">
                      +{teamMembers.length - 3}
                    </div>
                  )}
                </div>
              </div>

              <button
                onClick={() => {
                  updateTableState(table.id, {
                    editingRowId: null,
                    showAddForm: !tableState.showAddForm,
                  });
                }}
                className="flex items-center justify-center py-2 px-4 text-sm font-medium bg-transparent text-black border-2 border-[#0B333F] rounded-full hover:bg-primary hover:text-white transition duration-500 ease-in-out focus:outline-none focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 shadow-sm min-w-[200px] h-[40px]"
              >
                <svg
                  className="w-6 h-6 mr-2 -ml-3 flex items-center justify-center rounded-full bg-white border-2 border-black"
                  fill="none"
                  stroke="black"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Password
              </button>
            </div>

            {/* Add Password Form */}
            {tableState.showAddForm && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <AddPasswordCardForm
                  onCancel={() => {
                    updateTableState(table.id, {
                      showAddForm: false,
                      editingRowId: null,
                    });
                  }}
                  onSuccess={(newPasswordId) =>
                    handlePasswordSuccess(table.id, newPasswordId)
                  }
                  generatedPassword={generatedPassword}
                  passwordStrength={passwordStrength}
                  editData={
                    tableState.editingRowId
                      ? tableData.find(
                          (item) => item.id === tableState.editingRowId
                        )
                      : null
                  }
                />
              </div>
            )}

            {/* Table - Matching Member Index Design */}
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="overflow-x-auto">
                {tableData.length === 0 ? (
                  <div className="text-center py-12">
                    <p className="text-gray-500 mb-4">
                      No password cards added yet. Click "Add Password Card" to
                      add your first card.
                    </p>
                  </div>
                ) : (
                  <table className="w-full text-sm text-left">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th scope="col" className="px-4 py-3 w-12">
                          <input
                            type="checkbox"
                            checked={
                              tableState.shareableCards.length ===
                                tableData.length && tableData.length > 0
                            }
                            onChange={() => toggleAllShareable(table.id)}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          <div className="flex items-center">
                            Title
                            <svg
                              className="w-3 h-3 ml-1 text-gray-400"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          <div className="flex items-center">
                            User Name
                            <svg
                              className="w-3 h-3 ml-1 text-gray-400"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Password
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Team
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Department
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Level
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {tableData.map((card) => (
                        <TableRow
                          key={card.id}
                          card={card}
                          tableId={table.id}
                          isEditing={tableState.editingRowId === card.id}
                          isShareable={tableState.shareableCards.includes(
                            card.id
                          )}
                          visiblePassword={tableState.visiblePasswords[card.id]}
                          currentUserId={currentUserId}
                          onEdit={handleEdit}
                          onDelete={handleDelete}
                          onTogglePassword={togglePasswordVisibility}
                          onToggleShareable={toggleShareableCard}
                          onInlineEditSave={handleInlineEditSave}
                        />
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>

            {/* FIXED: Add New Password Card Button - Bottom Center for Each Table */}
            <div className="flex justify-center mt-6">
              <button
                onClick={() => {
                  updateTableState(table.id, {
                    editingRowId: null,
                    showAddForm: true,
                  });
                }}
                className="flex items-center gap-3 px-6 py-2 bg-[#006D94] text-white rounded-lg shadow-md hover:bg-[#005F80] transition duration-200"
              >
                {/* Circular Plus Icon */}
                <div className="w-6 h-6 flex items-center justify-center border-2 border-white rounded-full">
                  <span className=" relative -top-[2px] text-lg font-thin">+</span>
                </div>

                {/* Button Text */}
                <span className="text-sm font-medium">
                  Add New Password Card
                </span>
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

// TableRow component matching Member Index design
const TableRow = ({
  card,
  tableId,
  isEditing,
  isShareable,
  visiblePassword,
  currentUserId,
  onEdit,
  onDelete,
  onTogglePassword,
  onToggleShareable,
  onInlineEditSave,
}) => {
  // FIXED: Proper initialization of edit values
  const [editValues, setEditValues] = useState({
    title: card.password_title || card.title || card.user_name || "",
    username: card.username || card.user_name || "",
    team: card.team?.name || "",
    department: card.department?.name || "",
  });

  const handleInputChange = (field, value) => {
    setEditValues((prev) => ({ ...prev, [field]: value }));
  };

  // FIXED: Proper field value comparison and update
  const handleInputBlur = (field) => {
    const originalValue =
      field === "title"
        ? card.password_title || card.title || card.user_name
        : field === "username"
        ? card.username || card.user_name
        : field === "team"
        ? card.team?.name
        : field === "department"
        ? card.department?.name
        : "";

    if (
      editValues[field] !== originalValue &&
      editValues[field].trim() !== ""
    ) {
      onInlineEditSave(tableId, card.id, field, editValues[field]);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.target.blur();
    }
  };

  // Get password strength styling
  const getPasswordStrengthStyle = (level) => {
    switch (level) {
      case "Strong":
        return "bg-green-100 text-green-700 border border-green-200";
      case "Moderate":
        return "bg-yellow-100 text-yellow-700 border border-yellow-200";
      case "Weak":
        return "bg-red-100 text-red-700 border border-red-200";
      default:
        return "bg-green-100 text-green-700 border border-green-200";
    }
  };

  return (
    <tr className="hover:bg-gray-50 transition-colors">
      <td className="px-4 py-4">
        <input
          type="checkbox"
          checked={isShareable}
          onChange={() => onToggleShareable(tableId, card.id)}
          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
        />
      </td>

      {/* Title Column */}
      <td className="px-4 py-4">
        <div className="flex items-center">
          {/* Platform Icon Circle - Matching your design */}
          <div className="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center text-white font-medium mr-3 text-sm">
            {(card.password_title || card.title || card.user_name)
              ?.charAt(0)
              ?.toUpperCase() || "P"}
          </div>
          {isEditing ? (
            <input
              type="text"
              value={editValues.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              onBlur={() => handleInputBlur("title")}
              onKeyDown={handleKeyDown}
              className="font-medium text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              autoFocus
              placeholder="Platform/Service Title"
            />
          ) : (
            <span className="font-medium text-gray-900">
              {card.password_title || card.title || card.user_name}
            </span>
          )}
        </div>
      </td>

      {/* User Name Column */}
      <td className="px-4 py-4">
        <div className="flex items-center">
          {isEditing ? (
            <input
              type="text"
              value={editValues.username}
              onChange={(e) => handleInputChange("username", e.target.value)}
              onBlur={() => handleInputBlur("username")}
              onKeyDown={handleKeyDown}
              className="text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Username or Email"
            />
          ) : (
            <span className="text-gray-900 mr-2">
              {card.username || card.user_name}
            </span>
          )}
          {/* Copy Username Icon */}
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
            title="Copy username"
            onClick={() => {
              const username = card.username || card.user_name;
              if (username) {
                navigator.clipboard.writeText(username);
                toast.success("Username copied to clipboard!");
              }
            }}
          >
            <span className="material-symbols-outlined text-lg ">
              content_copy
            </span>
          </button>
        </div>
      </td>

      {/* Password Column */}
      <td className="px-4 py-4">
        <div className="flex items-center">
          <span className="text-gray-900 mr-2 font-mono">
            {visiblePassword ? card.password : "••••••••••••"}
          </span>
          {/* Eye Icon (using Material Symbols) */}
          <button
            onClick={() => onTogglePassword(tableId, card.id)}
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
            title={visiblePassword ? "Hide password" : "Show password"}
          >
            <span className="material-symbols-outlined text-lg">
              {visiblePassword ? "visibility_off" : "visibility"}
            </span>
          </button>
          {/* Copy Password Icon */}
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
            title="Copy password"
            onClick={() => {
              if (card.password) {
                navigator.clipboard.writeText(card.password);
                toast.success("Password copied to clipboard!");
              }
            }}
          >
            <span className="material-symbols-outlined text-lg ">
              content_copy
            </span>
          </button>
        </div>
      </td>

      {/* Team Column */}
      <td className="px-4 py-4">
        {isEditing ? (
          <input
            type="text"
            value={editValues.team}
            onChange={(e) => handleInputChange("team", e.target.value)}
            onBlur={() => handleInputBlur("team")}
            onKeyDown={handleKeyDown}
            className="text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        ) : (
          <span className="text-gray-900">
            {card.team?.name || "Team Name"}
          </span>
        )}
      </td>

      {/* Department Column */}
      <td className="px-4 py-4">
        {isEditing ? (
          <input
            type="text"
            value={editValues.department}
            onChange={(e) => handleInputChange("department", e.target.value)}
            onBlur={() => handleInputBlur("department")}
            onKeyDown={handleKeyDown}
            className="text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        ) : (
          <span className="text-gray-900">
            {card.department?.name || "Department name"}
          </span>
        )}
      </td>

      {/* Level Column - Matching your design colors */}
      <td className="px-4 py-4">
        <span
          className={`px-2 py-1 text-xs font-medium rounded ${getPasswordStrengthStyle(
            card.level
          )}`}
        >
          {card.level || "Strong Password"}
        </span>
      </td>

      {/* Action Column - Matching Member Index design */}
      <td className="px-4 py-4">
        <div className="flex items-center space-x-2">
          {/* Edit Icon - Matching your design */}
          {/* Edit Button */}
          <button
            onClick={() => onEdit(tableId, card.id)}
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
            title="Edit"
          >
            <span className="material-symbols-outlined text-base">
              stylus_note
            </span>
          </button>

          {/* Delete Icon - Only for card owner */}
          {card.user_id === currentUserId && (
            <button
              onClick={() => onDelete(card.id)}
              className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
              title="Delete"
            >
              <span className="material-symbols-outlined text-base">
                delete
              </span>
            </button>
          )}
        </div>
      </td>
    </tr>
  );
};

export default PasswordCardsTable;
