import React, { useEffect, useState } from "react";
import { Link, Navigate, useLocation, useNavigate } from "react-router-dom";
import Avatar from "./../assets/images/avatar.png";
import { useRoleBasedAccess } from "../common/useRoleBasedAccess";
import { API_URL } from './../common/fetchData/apiConfig';
import useFetchApiData from "./../common/fetchData/useFetchApiData.jsx";

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null;
};

const LeftSidebar = ({ isOpen }) => {
  const location = useLocation(); // Get the current location
  const [controlCenterOpen, setControlCenterOpen] = useState(true);
  const [teamManagementOpen, setTeamManagementOpen] = useState(true);
  const [taskAndProjectManagementOpen, setTaskAndProjectManagementOpen] = useState(true);
  const [resourceAndProjectManagementOpen, setResourceAndProjectManagementOpen] = useState(true);
  const [toolsAndProjectManagementOpen, setToolAndProjectManagementOpen] = useState(true);
  const [taskDetailsOpen, setTaskDetailsOpen] = useState(true);
  const [timeLoomOpen, setTimeLoomOpen] = useState(false);
  const [attendanceOpen, setAttendanceOpen] = useState(false);
  const [holidayOpen, setHolidayOpen] = useState(false);
  const [helpmenu, setHelpMenu] = useState(true);
  const [userData, setUserData] = useState('');
  const [error, setError] = useState(null); // Handle errors  
  const [loading, setLoading] = useState(true);

  const { rolePermissions } = useRoleBasedAccess();

  useEffect(() => {
    const token = localStorage.getItem('token');
  
    if (!isTokenValid()) {
      setError('No valid authentication token found.');
      setLoading(false);
      Navigate('/login');
      return; // Return early if no valid token exists
    }
  
    const fetchUserData = async () => {
      try {
        const response = await fetch(`${API_URL}/logged-users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,  // Pass token in the header
            'Content-Type': 'application/json',
          },
        });
  
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
  
        const data = await response.json();
  
        setUserData(data); // Set the user data
  
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
  
    fetchUserData();
  }, []);

  // Function to check if the current path matches the link's path
  const isActive = (path) => {
    return location.pathname === path ? "bg-gray-200 text-gray-900 font-bold" : "text-gray-700";
  };

  return (
    <div className="flex flex-col justify-between h-[100vh] overflow-y-auto relative scrollbar-vertical">
      <nav>
        {/* Control Center Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 cursor-pointer">
            <h5
              className={`w-full text-left font-bold text-sm text-gray-700 sidebarToggle transition-all duration-300 ease-in-out ${
                controlCenterOpen ? "opacity-100 scale-100 visible" : "opacity-100 scale-100 visible"
              }`}
              onClick={() => setControlCenterOpen(!controlCenterOpen)}
            >
              Control Center
            </h5>
            <span
              className={`material-symbols-rounded text-xl transition-transform duration-300 ease-in-out ${
                controlCenterOpen ? "rotate-180" : "rotate-0"
              }`}
            >
              keyboard_arrow_down
            </span>
          </div>

          {/* Dropdown Content */}
          <div
            className={`overflow-hidden transition-all duration-300 ease-in-out ${
              controlCenterOpen
                ? "max-h-[1000px] opacity-100 translate-y-0"
                : "max-h-0 opacity-0 translate-y-4"
            }`}
          >
            {/* Control Center Links */}
            <li className="list-none">
              <Link
                to="/"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4`}
              >
                <span className="material-symbols-rounded text-xl">view_comfy_alt</span>
                <div
                  className={`min-w-48 ${
                    controlCenterOpen ? "block" : "hidden"
                  } text-sm`}
                >
                  Dashboard
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/quickaccesshub"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4`}
              >
                <span className="material-symbols-rounded text-xl">rocket_launch</span>
                <div
                  className={`min-w-48 ${
                    controlCenterOpen ? "block" : "hidden"
                  } text-sm`}
                >
                  Quick Access Hub
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/noticeboard"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4`}
              >
                <span className="material-symbols-rounded text-xl">rocket_launch</span>
                <div
                  className={`min-w-48 ${
                    controlCenterOpen ? "block" : "hidden"
                  } text-sm`}
                >
                  Notice Board
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/surpriseme"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4`}
              >
                <span className="material-symbols-outlined">sentiment_very_satisfied</span>
                <div
                  className={`min-w-48 ${
                    controlCenterOpen ? "block" : "hidden"
                  } text-sm`}
                >
                  Surprise Me
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/notificationlog"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4`}
              >
                <span className="material-symbols-outlined">data_alert</span>
                <div
                  className={`min-w-48 ${
                    controlCenterOpen ? "block" : "hidden"
                  } text-sm`}
                >
                  Notification Log
                </div>
              </Link>
            </li>
          </div>
        </ul>

        {/* Team and People Management Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 cursor-pointer">
            <h5
              className={`w-full text-left font-bold text-sm text-gray-700 sidebarToggle transition-all duration-300 ease-in-out ${teamManagementOpen ? "opacity-100 scale-100 visible" : "opacity-100 scale-100 visible"}`}
              onClick={() => setTeamManagementOpen(!teamManagementOpen)}
            >
              Team and People Management
            </h5>
            <span
              className={`material-symbols-rounded text-xl transition-transform duration-300 ease-in-out ${teamManagementOpen ? "rotate-180" : "rotate-0"}`}
            >
              keyboard_arrow_down
            </span>
          </div>

          {/* Dropdown Content */}
          <div
            className={`overflow-hidden transition-all duration-300 ease-in-out ${teamManagementOpen ? "max-h-[1000px] opacity-100 translate-y-0" : "max-h-0 opacity-0 translate-y-4"}`}
          >
            {/* Team and People Management Links */}
            <li className="list-none">
              <Link
                to="/team-members"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/team-members")}`}
              >
                <span className="material-symbols-rounded text-xl">diversity_1</span>
                <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                  Team Members
                </div>
              </Link>
            </li>

            {rolePermissions.hasTeamLeadRole && (
              <li className="list-none">
                <Link
                  to="/member-index"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/member-index")}`}
                >
                  <span className="material-symbols-rounded text-xl">diversity_2</span>
                  <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                    Member Index
                  </div>
                </Link>
              </li>
            )}

            <li className="list-none">
              <Link
                to="/team-snapshot"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/team-snapshot")}`}
              >
                <span className="material-symbols-rounded text-xl">diversity_2</span>
                <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                  Team Snapshot
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/schedule-planners"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/schedule-planners")}`}
              >
                <span className="material-symbols-rounded text-xl">diversity_2</span>
                <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                  Schedule Planner
                </div>
              </Link>
            </li>

            <li className="list-none">
              <div
                className="px-4 py-3 hover:bg-gray-200 flex flex-row items-center justify-between gap-4"
                onClick={() => setAttendanceOpen(!attendanceOpen)}
              >
                <span className={`material-symbols-rounded text-xl ${teamManagementOpen ? "block" : "hidden"} transition-all duration-300 ease-in-out`}>
                  co_present
                </span>
                <div className={`min-w-48 font-bold cursor-pointer ${teamManagementOpen ? "block" : "hidden"} transition-all duration-300 ease-in-out text-sm`}>
                  Attendance
                </div>
                <span className="material-symbols-rounded text-xl">remove</span>
              </div>
            </li>

            {attendanceOpen && (
              <div className="px-4">
                <li className="list-none">
                  <Link
                    to="/attendance"
                    className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/attendance")}`}
                  >
                    <span className={`material-symbols-rounded text-xl ${teamManagementOpen ? "block" : "hidden"}`}>
                      co_present
                    </span>
                    <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                      Attendance
                    </div>
                  </Link>
                </li>

                <li className="list-none">
                  <Link
                    to="/attendance-formation"
                    className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/attendance-formation")}`}
                  >
                    <span className={`material-symbols-rounded text-xl ${teamManagementOpen ? "block" : "hidden"}`}>
                      co_present
                    </span>
                    <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                      Attendance Formation
                    </div>
                  </Link>
                </li>
              </div>
            )}

            <li className="list-none">
              <div
                className="px-4 py-3 hover:bg-gray-200 flex flex-row items-center justify-between gap-4"
                onClick={() => setHolidayOpen(!holidayOpen)}
              >
                <span className={`material-symbols-rounded text-xl ${teamManagementOpen ? "block" : "hidden"}`}>
                  co_present
                </span>
                <div className={`min-w-48 font-bold cursor-pointer ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                  Holiday Calendar
                </div>
                <span className="material-symbols-rounded text-xl">remove</span>
              </div>
            </li>

            {holidayOpen && (
              <div className="px-4">
                <li className="list-none">
                  <Link
                    to="/holidaycalenders"
                    className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/holidaycalenders")}`}
                  >
                    <span className={`material-symbols-rounded text-xl ${teamManagementOpen ? "block" : "hidden"}`}>
                      co_present
                    </span>
                    <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                      Holiday List
                    </div>
                  </Link>
                </li>

                <li className="list-none">
                  <Link
                    to="/events"
                    className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/events")}`}
                  >
                    <span className={`material-symbols-rounded text-xl ${teamManagementOpen ? "block" : "hidden"}`}>
                      co_present
                    </span>
                    <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                      Events
                    </div>
                  </Link>
                </li>

                <li className="list-none">
                  <Link
                    to="/seat-plan"
                    className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/events")}`}
                  >
                    <span className="material-symbols-outlined">chair</span>
                    <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                      Seat Plan
                    </div>
                  </Link>
                </li>
              </div>
            )}

            <li className="list-none">
              <Link
                to="/birthday"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/birthday")}`}
              >
                <span className="material-symbols-rounded text-xl">calendar_month</span>
                <div className={`min-w-48 text-slate-400 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                  Birthday
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/bloodbank"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/bloodbank")}`}
              >
                <span className="material-symbols-rounded text-xl">calendar_month</span>
                <div className={`min-w-48 text-slate-400 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                  Blood Bank
                </div>
              </Link>
            </li>

            {rolePermissions.hasShiftLeadRole && (
              <li className="list-none">
                <Link
                  to="/reporters"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/reporters")}`}
                >
                  <span className="material-symbols-rounded text-xl">group</span>
                  <div className={`min-w-48 ${teamManagementOpen ? "block" : "hidden"} text-sm`}>
                    Reporter Directory
                  </div>
                </Link>
              </li>
            )}
          </div>
        </ul>

        {/* Task and Project Management Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 cursor-pointer">
            <h5
              className={`w-full text-left font-bold text-sm text-gray-700 sidebarToggle transition-all duration-300 ease-in-out ${taskAndProjectManagementOpen ? "opacity-100 scale-100 visible" : "opacity-100 scale-100 visible"}`}
              onClick={() => setTaskAndProjectManagementOpen(!taskAndProjectManagementOpen)}
            >
              Task and Project Management
            </h5>
            <span
              className={`material-symbols-rounded text-xl transition-transform duration-300 ease-in-out ${taskAndProjectManagementOpen ? "rotate-180" : "rotate-0"}`}
            >
              keyboard_arrow_down
            </span>
          </div>

          {/* Dropdown Content */}
          <div
            className={`overflow-hidden transition-all duration-300 ease-in-out ${taskAndProjectManagementOpen ? "max-h-[1000px] opacity-100 translate-y-0" : "max-h-0 opacity-0 translate-y-4"}`}
          >
            {/* Task and Project Management Links */}
            <li className="list-none">
              <div
                className="px-4 py-3 hover:bg-gray-200 flex flex-row items-center justify-between gap-4"
                onClick={() => setTaskDetailsOpen(!taskDetailsOpen)}
              >
                <span className="material-symbols-rounded text-xl">task</span>
                <div
                  className={`min-w-48 font-bold cursor-pointer ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Task Details
                </div>
                <span
                  className={`material-symbols-rounded text-xl ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out`}
                >
                  remove
                </span>
              </div>
            </li>

            {taskDetailsOpen && (
              <div className="px-4">
                {rolePermissions.hasShiftLeadRole && (
                  <li className="list-none">
                    <Link
                      to="/task-records"
                      className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/task-records")}`}
                    >
                      <span className="material-symbols-rounded text-xl">task</span>
                      <div
                        className={`min-w-48 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                      >
                        Task Record
                      </div>
                    </Link>
                  </li>
                )}

                {rolePermissions.hasTeamLeadRole && (
                  <li className="list-none">
                    <Link
                      to="/formation"
                      className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/formation")}`}
                    >
                      <span className="material-symbols-rounded text-xl">list_alt</span>
                      <div
                        className={`min-w-48 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                      >
                        Formation
                      </div>
                    </Link>
                  </li>
                )}
              </div>
            )}

            <li className="list-none">
              <Link
                to="/time-cards"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/time-cards")}`}
              >
                <span className="material-symbols-rounded text-xl">schedule</span>
                <div
                  className={`min-w-48 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Time Cards
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/tasky"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/tasky")}`}
              >
                <span className="material-symbols-rounded text-xl">group_add</span>
                <div
                  className={`min-w-48 text-slate-400 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Tasky
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/achieve"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/achieve")}`}
              >
                <span className="material-symbols-rounded text-xl">task_alt</span>
                <div
                  className={`min-w-48 text-slate-400 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Achieve+
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/quick-note"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/quick-note")}`}
              >
                <span className="material-symbols-rounded text-xl">edit_note</span>
                <div
                  className={`min-w-48 text-slate-400 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Quick Note
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/calender"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/calender")}`}
              >
                <span className="material-symbols-rounded text-xl">calendar_today</span>
                <div
                  className={`min-w-48 text-slate-400 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Calendar
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/qa-pool"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/qa-pool")}`}
              >
                <span className="material-symbols-rounded text-xl">high_quality</span>
                <div
                  className={`min-w-48 text-slate-400 ${taskAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  QA Pool
                </div>
              </Link>
            </li>
          </div>
        </ul>

        {/* Resources and Document Management Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 cursor-pointer">
            <h5
              className={`w-full text-left font-bold text-sm text-gray-700 sidebarToggle transition-all duration-300 ease-in-out ${resourceAndProjectManagementOpen ? "opacity-100 scale-100 visible" : "opacity-100 scale-100 visible"}`}
              onClick={() => setResourceAndProjectManagementOpen(!resourceAndProjectManagementOpen)}
            >
              Resources & Document Managements
            </h5>
            <span
              className={`material-symbols-rounded text-xl transition-transform duration-300 ease-in-out ${resourceAndProjectManagementOpen ? "rotate-180" : "rotate-0"}`}
            >
              keyboard_arrow_down
            </span>
          </div>

          {/* Dropdown Content */}
          <div
            className={`overflow-hidden transition-all duration-300 ease-in-out ${resourceAndProjectManagementOpen ? "max-h-[1000px] opacity-100 translate-y-0" : "max-h-0 opacity-0 translate-y-4"}`}
          >
            <li className="list-none">
              <Link
                to="/device-inventory"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/device-inventory")}`}
              >
                <span className="material-symbols-rounded text-xl">inventory</span>
                <div
                  className={`min-w-48 text-slate-400 ${resourceAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Device Inventory
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/documents-hub"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/documents-hub")}`}
              >
                <span className="material-symbols-rounded text-xl">document_scanner</span>
                <div
                  className={`min-w-48 text-slate-400 ${resourceAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Documents Hub
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/training"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/training")}`}
              >
                <span className="material-symbols-rounded text-xl">on_device_training</span>
                <div
                  className={`min-w-48 text-slate-400 ${resourceAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Training Management
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/kpi-management"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/kpi-management")}`}
              >
                <span className="material-symbols-rounded text-xl">manage_history</span>
                <div
                  className={`min-w-48 text-slate-400 ${resourceAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  KPI Management
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/census-report"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/census-report")}`}
              >
                <span className="material-symbols-rounded text-xl">summarize</span>
                <div
                  className={`min-w-48 text-slate-400 ${resourceAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Census Report
                </div>
              </Link>
            </li>

            <li className="list-none">
              <Link
                to="/payroll"
                className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive("/payroll")}`}
              >
                <span className="material-symbols-rounded text-xl">payments</span>
                <div
                  className={`min-w-48 text-slate-400 ${resourceAndProjectManagementOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                  Payroll
                </div>
              </Link>
            </li>
          </div>
        </ul>

        {/* Tools & Utilities Dropdown)}*/}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 cursor-pointer">
            <h5
              className={`w-full text-left  font-bold text-sm text-gray-700 ${isOpen ? "block" : "hidden"
                } sidebarToggle transition-all duration-300 ease-in-out`}
              onClick={() =>
                setToolAndProjectManagementOpen(!toolsAndProjectManagementOpen)
              }
            >
              Tools & Utilities
            </h5>
            <span className="material-symbols-rounded text-xl">
              keyboard_arrow_down
            </span>
          </div>
        </ul>

        {/* Settings Link */}
        <ul className="flex justify-start flex-col text-left">
          {toolsAndProjectManagementOpen && (
            <>
              <li className="list-none">
                <div
                  className="px-4 py-3 hover:bg-gray-200 flex flex-row items-center  justify-between gap-4"
                  onClick={() => setTimeLoomOpen(!timeLoomOpen)}
                >
                  <span className="material-symbols-rounded text-xl">
                    globe
                  </span>
                  <div
                    className={`min-w-48 font-bold cursor-pointer ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    Time Loom
                  </div>
                  <span className={`material-symbols-rounded text-xl ${isOpen ? "block" : "hidden"
                        } sidebarToggle transition-all duration-300 ease-in-out`}>
                    remove
                  </span>
                </div>
              </li>
              {timeLoomOpen && (
                <div className="px-4">
                  <li className="list-none">
                    <Link
                      to="/world-time"
                      className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                        "/world-time"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl">
                        globe
                      </span>
                      <div
                        className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                          } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                      >
                        World Time
                      </div>
                    </Link>
                  </li>
                  <li className="list-none">
                    <Link
                      to="/time-zone-convert"
                      className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                        "/time-zone-convert"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl">
                        globe
                      </span>
                      <div
                        className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                          } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                      >
                        Time Zone Converter
                      </div>
                    </Link>
                  </li>
                </div>
              )}

              <li className="list-none">
                <Link
                  to="/password-manager"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/password-manager"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl">
                    encrypted
                  </span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    Password Manager
                  </div>
                </Link>
              </li>
              <li className="list-none">
                <Link
                  to="/external-tools"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/external-tools"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl">
                    encrypted
                  </span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    External Tools
                  </div>
                </Link>
              </li>

              {rolePermissions.hasAdminRole && (
              <li className="list-none">
                <Link
                  to="/settings"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/settings"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl">tune</span>
                  <div
                    className={`min-w-48 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    Settings
                  </div>
                </Link>
              </li>
              )}
              
            </>
          )}
        </ul>

        {/* Control Help Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 cursor-pointer">
            <h5
              className={`w-full text-left font-bold text-sm text-gray-700 ${isOpen ? "block" : "hidden"
                } sidebarToggle transition-all duration-300 ease-in-out`}
              onClick={() => setHelpMenu(!helpmenu)}
            >
              Help
            </h5>
            <span className="material-symbols-rounded text-xl">
              keyboard_arrow_down
            </span>
          </div>

          {helpmenu && (
            <>
              <li className="list-none">
                <Link
                  to="/activity-log"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/activity-log"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl">browse_activity</span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    Activity Log
                  </div>
                </Link>
              </li>

              <li className="list-none">
                <Link
                  to="/change-log"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/change-log"
                  )}`}
                >
                  <span className="material-symbols-outlined">monitoring</span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    Change Log
                  </div>
                </Link>
              </li>

              <li className="list-none">
                <Link
                  to="/app-support"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/app-support"
                  )}`}
                >
                  <span className="material-symbols-outlined">app_promo</span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    App Support
                  </div>
                </Link>
              </li>

              <li className="list-none">
                <Link
                  to="/about-the-app"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/about-the-app"
                  )}`}
                >
                  <span className="material-symbols-outlined">monitoring</span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    About The App
                  </div>
                </Link>
              </li>

              <li className="list-none">
                <Link
                  to="/report-problem"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/report-problem"
                  )}`}
                >
                  <span className="material-symbols-outlined">report</span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    Report Problem
                  </div>
                </Link>
              </li>

              <li className="list-none">
                <Link
                  to="/give-feedback"
                  className={`px-4 py-3 hover:bg-gray-200 flex flex-row items-center gap-4 ${isActive(
                    "/give-feedback"
                  )}`}
                >
                  <span className="material-symbols-outlined">feedback</span>
                  <div
                    className={`min-w-48 text-slate-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                  >
                    Give Feedback
                  </div>
                </Link>
              </li>
            </>
          )}
        </ul>

      </nav>
      {/* Sticky Profile */}
      <div
        className={`px-4 flex flex-col fixed bottom-0 left-0 items-center justify-center rounded-xl ${isOpen ? "py-4 bg-white" : "py-2 bg-transparent"
        } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
    >
        <Link to="/profile" className="flex flex-row justify-center gap-4">
              {loading ? (
                  <div>Loading...</div>
                ) : error ? (
                  <div>{error}</div>
                ) : userData ? (
                  userData.photo ? (
                    <img
                    className={`m-auto text-sm w-12 rounded-full ${isOpen ? "w-10" : "w-5"
                    } sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                      src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${userData.photo}`}
                      alt="Avatar"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-primary text-white flex justify-center items-center text-center text-sm rounded-full">No Photo</div>
                  )
                ) : (
                  <div>No User Data</div>
                )}
            <div className="text-left">
                <h4
                    className={`min-w-48 text-slate-400 font-regular text-base ${isOpen ? "block" : "hidden"
                    } sidebarToggle transition-all duration-300 ease-in-out text-sm whitespace-nowrap`}
                >
                    {userData.fname} {userData.lname}
                </h4>
                <p
                    className={`${isOpen ? "block" : "hidden"} sidebarToggle transition-all duration-300 ease-in-out text-sm`}
                >
                    {userData?.designations?.length > 0 ? userData?.designations[0]?.name : "Not Found"}
                </p>
            </div>
        </Link>
    </div>

    </div>
  );
};

export default LeftSidebar;
