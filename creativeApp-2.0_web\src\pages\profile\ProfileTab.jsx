import React, { useEffect, useState } from "react";
import SingleUserData from "./SingleUserData";
import FetchLoggedInUser from "../../common/fetchData/fetchLoggedInUser";
import EditLoggedInUser from "../team-member/EditLoggedInUser"; // Import EditLoggedInUser modal
import { useNavigate } from 'react-router-dom'
import {DateTimeFormatTable} from './../../common/DateTimeFormatTable';
import ChangePassword from "../../common/login/ChangePassword";
import Loading from "../../common/Loading";
import { API_URL } from './../../common/fetchData/apiConfig'; 

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null && token !== ''; // Ensuring that the token is not empty
};

const ProfileTab = () => {
  const [open, setOpen] = useState("profile");
  const [visible, setVisible] = useState(false); // Controls visibility of the modal
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);

  // Fetch logged-in user's data from the API
  useEffect(() => {
    const token = localStorage.getItem('token');

    if (!isTokenValid()) {
      setError('No valid authentication token found.');
      setLoading(false);
      navigate('/login');
      return; // Return early if no valid token exists
    }

    const fetchUserData = async () => {

      setFilterOptionLoading(true);

      try {
        const response = await fetch(`${API_URL}/logged-users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,  // Pass token in the header
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }

        const data = await response.json();

        setUserData(data);

      } catch (error) {
        setError(error.message);
      } finally {
         setFilterOptionLoading(false);
      }
    };

    fetchUserData();
  }, []);

    // If loading users or filter options, show loading component
    if (filterOptionLoading) {
      return <Loading />;
  }

  // If there's an error, show the error message
  if (error) {
    return <div>Error: {error}</div>;
  }

  const handleTabOpen = (tabCategory) => {
    setOpen(tabCategory);
  };

  return (
    <section className="dark:bg-dark">
      <div className="container">
        <div className="-mx-4 flex flex-wrap">
          <div className="w-full px-4">
            <div className="mb-14 w-full">
              <div className="flex flex-col flex-wrap justify-between bg-gray-100 dark:bg-gray-400 sm:flex-row">
                <div className="flex flex-row items-center">
                  <button
                    onClick={() => handleTabOpen("profile")}
                    className={`min-w-[190px]  text-center justify-center items-center  py-4 px-8 text-sm font-medium  border-b-4 border-primary text-gray-800 flex flex-row ${open === "profile"
                      ? "text-primary border-b-4 border-blue rounded-sm"
                      : " min-w-[190px]  text-center justify-center items-center  py-4 px-8 text-sm font-medium  border-b-4 border-transparent hover:border-primary text-primary"
                      }`}
                  >
                    <span className="material-symbols-rounded text-primary pr-2 text-xl">home</span>Home
                  </button>
                  <button
                    onClick={() => handleTabOpen("password")}
                    className={`min-w-[190px]  text-center justify-center items-center  py-4 px-8 text-sm font-medium  border-b-4 border-primary text-gray-800 flex flex-row ${open === "password"
                      ? "text-primary border-b-4 border-blue rounded-sm"
                      : " min-w-[190px]  text-center justify-center items-center  py-4 px-8 text-sm font-medium  border-b-4 border-transparent hover:border-primary text-primary"
                      }`}
                  >
                    <span className="material-symbols-rounded text-primary pr-2 text-xl">business_center</span> Password
                  </button>
                </div>
              </div>

              {/* Tab Content */}
              <div className="">
                <TabContent tabCategory="profile" open={open}>
                  {/* Profile Intro */}
                  {userData ? (
                    <div className="flex flex-row w-full gap-8">
                      <div className="flex flex-row justify-between w-full border border-gray-300 p-12 rounded-2xl">
                        <div className="flex flex-row justify-center gap-8 items-center">
                          <div className="w-40 h-40 overflow-hidden rounded-full bg-gray-100 flex items-center justify-center">
                            <img src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${userData.photo}`} alt="Profile" className="w-auto" />
                          </div>
                          <div className="text-left">
                            <h3 className="text-2xl font-medium pb-4">{userData.fname} {userData.lname}</h3>
                            <p className="text-xl pb-4">{userData.designations?.[0]?.name}</p>
                            <span className="bg-gray-200 py-3 px-4 text-primary text-base rounded-full">{userData?.resource_types?.[0]?.name || "Responsibility level found"}</span>
                          </div>
                        </div>
                        <button
                          className="border border-gray-300 p-6 text-gray-900 text-base rounded-full h-10 flex flex-row items-center"
                          onClick={() => setVisible(true)} // Open modal on click
                        >
                          <span className="material-symbols-rounded text-xl pr-2">draw</span>Edit
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>No user data available</div>
                  )}

                  {/* About */}
                  {userData ? (
                    <div className="flex flex-row justify-between border border-gray-300 p-12 rounded-2xl mt-6">
                      <div className="flex flex-row justify-center gap-8 items-center">
                        <div className="w-full rounded-lg">
                          <h3 className="text-xl font-bold text-left pb-4">Personal Information</h3>
                          <div className="pb-6 text-left">
                            <p className="text-gray-700">About me</p>
                            <p className="text-gray-800 font-medium">{userData.about}</p>
                          </div>
                          <div className="flex flex-row justify-between items-start flex-wrap gap-6">
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">First Name</p>
                              <p className="text-gray-900 font-medium">{userData.fname}</p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Last Name</p>
                              <p className="text-gray-900 font-medium">{userData.lname}</p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Employee ID</p>
                              <p className="text-gray-900 font-medium">{userData.eid}</p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Birthday</p>
                              <p className="text-gray-900 font-medium">
                                {DateTimeFormatTable(userData.birthday)}
                              </p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Gender</p>
                              <p className="text-gray-900 font-medium">{userData.gender}</p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Birthday Celebration</p>
                              <p className="text-gray-900 font-medium">{userData.birthday_celebration}</p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Preferred Pronoun</p>
                              <p className="text-gray-900 font-medium">{userData.nick_name}</p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Blood Group</p>
                              <p className="text-gray-900 font-medium">{userData.bloods?.[0]?.name}</p>
                            </div>
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Marital Status</p>
                              <p className="text-gray-900 font-medium">Married</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>No user data available</div>
                  )}

                  {/* Contact Information */}
                  {userData ? (
                    <div className="flex flex-row justify-between border border-gray-300 p-12 rounded-2xl mt-6">
                      <div className="flex flex-row justify-center gap-8 items-center">
                        <div className="w-full rounded-lg">
                          <h3 className="text-xl font-bold text-left pb-4">Contact Information</h3>
                          {/* Education */}
                          {/* <div className="pb-6 text-left">
                              <p className="text-gray-900">Education</p>
                              <p className="text-gray-900 font-medium">Thomas Jeff High School, Stanford University</p>
                            </div> */}
                          <div className="flex flex-row justify-between items-start flex-wrap gap-6">
                            {/* Email */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Email</p>
                              <p className="text-gray-900 font-medium">{userData.email}</p>
                            </div>
                            {/* Emergency Contact */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Emergency Contact Number</p>
                              <p className="text-gray-900 font-medium">{userData.emergency_contact}</p>
                            </div>
                            {/* Primary Contact */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Primary Phone Number</p>
                              <p className="text-gray-900 font-medium">{userData.primary_contact}</p>
                            </div>
                            {/* Relation with Contact */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Emergency Contact Relationship</p>
                              <p className="text-gray-900 font-medium">{userData.relation_contact}</p>
                            </div>
                            {/* Secondary Contact */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Secondary Contact Number</p>
                              <p className="text-gray-900 font-medium">{userData.emergency_contact}</p>
                            </div>
                            {/* Present Address*/}
                            <div className="text-left min-w-[100%]">
                              <p className="text-gray-700">Present Address</p>
                              <p className="text-gray-900 font-medium">{userData.present_address}</p>
                            </div>
                            {/* Present Address*/}
                            <div className="text-left min-w-[100%]">
                              <p className="text-gray-700">Permanent Address</p>
                              <p className="text-gray-900 font-medium">{userData.permanent_address}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* <button className="border border-gray-300 p-6 text-gray-900 text-base rounded-full h-10 flex flex-row items-center"><span className="material-symbols-rounded text-xl pr-2">draw</span>Edit</button> */}
                    </div>
                  ) : (
                    <div>No user data available</div>
                  )}

                  {/* Workspace Details*/}
                  {userData ? (
                    <div className="flex flex-row justify-between border border-gray-300 p-12 rounded-2xl mt-6">
                      <div className="flex flex-row justify-center gap-8 items-center">
                        <div className="w-full rounded-lg">
                          <h3 className="text-xl font-bold text-left pb-4">Employment Details</h3>
                          <div className="flex flex-row justify-between items-start flex-wrap gap-6">
                            {/* Previous Designation in the Company */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Previous Designation in the Company</p>
                              <p className="text-gray-900 font-medium">{userData.prev_designation}</p>
                            </div>
                            {/* Department */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Department</p>
                              <p className="text-gray-900 font-medium">{userData?.departments?.[0]?.name || "Department not found"}</p>
                            </div>
                            {/* Team */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Team</p>
                              <p className="text-gray-900 font-medium">
                                {
                                  userData?.teams?.find(team => team.pivot?.is_default === 1)?.name
                                  || "Team not found"
                                }
                              </p>
                            </div>

                            {/* POC */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">POC</p>
                              <p className="text-gray-900 font-medium">{userData?.teams?.[0]?.poc || "POC not found"}</p>
                            </div>
                            {/* Manager*/}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Manager</p>
                              <p className="text-gray-900 font-medium">{userData?.teams?.[0]?.manager || "Team lead not found"}</p>
                            </div>
                            {/* Team Lead/Report to */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Team Lead/Report to</p>
                              <p className="text-gray-900 font-medium">{userData?.teams?.[0]?.team_lead || "Team lead not found"}</p>
                            </div>
                            {/* Billing Status */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Billing Status</p>
                              <p className="text-gray-900 font-medium">{userData?.billing_statuses?.[0]?.name || "Billing Status not found"}</p>
                            </div>
                            {/* Team Member Status*/}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Team Member Status</p>
                              <p className="text-gray-900 font-medium">{userData?.member_statuses?.[0]?.name || "Team Member Status not found"}</p>
                            </div>
                            {/* Contract Type*/}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Contract Type</p>
                              <p className="text-gray-900 font-medium">{userData?.contact_types?.[0]?.name || "Contact Type not found"}</p>
                            </div>
                            {/* Joining Date */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Joining Date</p>
                              <p className="text-gray-900 font-medium">
                                {DateTimeFormatTable(userData.joining_date)}
                              </p>
                            </div>
                            {/* Resource Status*/}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Resource Status</p>
                              <p className="text-gray-900 font-medium">{userData?.resource_statuses?.[0]?.name || "Resource Status not found"}</p>
                            </div>
                            {/* Availablity Status*/}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Availablity Status</p>
                              <p className="text-gray-900 font-medium">{userData?.available_statuses?.[0]?.name || "Resource Status not found"}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* <button className="border border-gray-300 p-6 text-gray-900 text-base rounded-full h-10 flex flex-row items-center"><span className="material-symbols-rounded text-xl pr-2">draw</span>Edit</button> */}
                    </div>
                  ) : (
                    <div>No user data available</div>
                  )}

                  {/* Employment Details*/}
                  {userData ? (
                    <div className="flex flex-row justify-between border border-gray-300 p-12 rounded-2xl mt-6">
                      <div className="flex flex-row justify-center gap-8 items-center w-full">
                        <div className="w-full rounded-lg">
                          <h3 className="text-xl font-bold text-left pb-4">Workspace Details</h3>
                          <div className="flex flex-row justify-between items-start flex-wrap gap-6">
                            {/* Desk ID*/}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Desk ID</p>
                              <p className="text-gray-900 font-medium">{userData.desk_id}</p>
                            </div>
                            {/* Shift */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Shift</p>
                              <p className="text-gray-900 font-medium">{userData?.schedules?.[0]?.shift_name || "Shift not found"}</p>
                            </div>
                            {/* Work Location */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Work Location</p>
                              <p className="text-gray-900 font-medium">{userData?.branches?.[0]?.locations?.[0]?.locations_name || "Location not found"}</p>
                            </div>
                            {/* Branch */}
                            <div className="text-left min-w-[45%]">
                              <p className="text-gray-700">Office Branch</p>
                              <p className="text-gray-900 font-medium">{userData?.branches?.[0]?.name || "Office Branch not found"}</p>
                            </div>

                          </div>
                        </div>
                      </div>
                      {/* <button className="border border-gray-300 p-6 text-gray-900 text-base rounded-full h-10 flex flex-row items-center"><span className="material-symbols-rounded text-xl pr-2">draw</span>Edit</button> */}
                    </div>
                  ) : (
                    <div>No user data available</div>
                  )}

                </TabContent>

                <TabContent tabCategory="password" open={open}>
                  <div className="flex flex-col md:flex-row gap-8">
                    <ChangePassword />
                  </div>
                </TabContent>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal for Editing User Data */}
      <EditLoggedInUser
        visible={visible}
        setVisible={setVisible}
        userData={userData || {}}
      />

    </section>
  );
};

const TabContent = ({ open, tabCategory, details, children }) => {
  return (
    <div
      className={`py-6 text-base leading-relaxed text-body-color dark:text-dark-6 ${open === tabCategory ? "block" : "hidden"
        }`}
    >
      {details && <p>{details}</p>}
      {children && <div>{children}</div>}
    </div>
  );
};

export default ProfileTab;
