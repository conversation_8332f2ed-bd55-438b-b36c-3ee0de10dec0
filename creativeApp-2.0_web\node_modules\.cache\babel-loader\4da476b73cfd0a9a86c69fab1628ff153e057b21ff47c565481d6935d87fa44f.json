{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordCardsTable.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport AddPasswordCardForm from \"./AddPasswordCardForm\";\nimport { confirmationAlert } from \"../../common/coreui\";\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\nimport { useGetPasswordManagerDataQuery, useDeletePasswordManagerMutation, useUpdatePasswordManagerMutation } from \"../../features/api/passwordManagerApi\";\nimport { toast } from \"sonner\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PasswordCardsTable = ({\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  // Get current user data\n  const {\n    userData\n  } = FetchLoggedInRole();\n  const currentUserId = userData === null || userData === void 0 ? void 0 : userData.id;\n\n  // API hooks\n  const {\n    data: dataItems,\n    isLoading,\n    refetch\n  } = useGetPasswordManagerDataQuery({\n    sort_by: \"created_at\",\n    order: \"desc\",\n    page: 1,\n    per_page: 100,\n    query: \"\"\n  });\n  const [deletePasswordManager] = useDeletePasswordManagerMutation();\n  const [updatePasswordManager] = useUpdatePasswordManagerMutation();\n\n  // State for managing multiple tables\n  const [tables, setTables] = useState([{\n    id: 1,\n    name: \"Teams Password Card\"\n  }]);\n\n  // State for each table\n  const [tableStates, setTableStates] = useState({\n    1: {\n      showAddForm: false,\n      editingRowId: null,\n      visiblePasswords: {},\n      shareableCards: [],\n      editingHeader: false,\n      headerTitle: \"Teams Password Card\"\n    }\n  });\n\n  // State to track which passwords belong to which table\n  const [tablePasswordAssociations, setTablePasswordAssociations] = useState({});\n  const [initializedExistingPasswords, setInitializedExistingPasswords] = useState(false);\n\n  // Initialize existing passwords to the first table\n  useEffect(() => {\n    if (!initializedExistingPasswords && dataItems !== null && dataItems !== void 0 && dataItems.data && dataItems.data.length > 0) {\n      const existingPasswordIds = dataItems.data.map(item => item.id);\n      setTablePasswordAssociations(prev => ({\n        ...prev,\n        1: existingPasswordIds\n      }));\n      setInitializedExistingPasswords(true);\n    }\n  }, [dataItems, initializedExistingPasswords]);\n\n  // Get state for specific table\n  const getTableState = tableId => {\n    return tableStates[tableId] || {\n      showAddForm: false,\n      editingRowId: null,\n      visiblePasswords: {},\n      shareableCards: [],\n      editingHeader: false,\n      headerTitle: `Teams Password Card ${tableId}`\n    };\n  };\n\n  // Update state for specific table\n  const updateTableState = (tableId, updates) => {\n    setTableStates(prev => ({\n      ...prev,\n      [tableId]: {\n        ...getTableState(tableId),\n        ...updates\n      }\n    }));\n  };\n\n  // Get passwords for a specific table\n  const getTableData = tableId => {\n    const allData = (dataItems === null || dataItems === void 0 ? void 0 : dataItems.data) || [];\n    const tablePasswords = tablePasswordAssociations[tableId] || [];\n    return allData.filter(item => tablePasswords.includes(item.id));\n  };\n\n  // Associate a password with a table\n  const associatePasswordWithTable = (tableId, passwordId) => {\n    setTablePasswordAssociations(prev => ({\n      ...prev,\n      [tableId]: [...(prev[tableId] || []), passwordId]\n    }));\n  };\n\n  // Remove password association from table\n  const removePasswordFromTable = (tableId, passwordId) => {\n    setTablePasswordAssociations(prev => ({\n      ...prev,\n      [tableId]: (prev[tableId] || []).filter(id => id !== passwordId)\n    }));\n  };\n\n  // Delete table\n  const handleDeleteTable = tableId => {\n    if (tables.length === 1) {\n      toast.error(\"Cannot delete the last table. At least one table must remain.\");\n      return;\n    }\n    setTables(prev => prev.filter(table => table.id !== tableId));\n\n    // Remove state for deleted table\n    setTableStates(prev => {\n      const newState = {\n        ...prev\n      };\n      delete newState[tableId];\n      return newState;\n    });\n    toast.success(\"Password table deleted!\");\n  };\n\n  // Get team members for avatar display - FIXED: Proper backend integration\n  const getTeamMembers = () => {\n    const members = [];\n\n    // Add current user first\n    if (userData) {\n      members.push({\n        id: userData.id,\n        fname: userData.fname || \"User\",\n        lname: userData.lname || \"\",\n        photo: userData.photo || null\n      });\n    }\n\n    // FIXED: Add team members from backend data with proper structure\n    if (userData !== null && userData !== void 0 && userData.teams && userData.teams.length > 0) {\n      userData.teams.forEach(team => {\n        // Check if team has users (members) loaded\n        if (team.users && Array.isArray(team.users)) {\n          team.users.forEach(member => {\n            // Avoid duplicate current user\n            if (member.id !== userData.id) {\n              members.push({\n                id: member.id,\n                fname: member.fname || \"Team\",\n                lname: member.lname || \"Member\",\n                photo: member.photo || null\n              });\n            }\n          });\n        }\n      });\n    }\n\n    // If no team members found, add some placeholder members for demo\n    if (members.length === 1) {\n      for (let i = 2; i <= 4; i++) {\n        members.push({\n          id: `placeholder_${i}`,\n          fname: `Team`,\n          lname: `Member ${i}`,\n          photo: null\n        });\n      }\n    }\n    return members.slice(0, 4); // Show max 4 avatars\n  };\n\n  // Toggle password visibility for specific table\n  const togglePasswordVisibility = (tableId, cardId) => {\n    const currentState = getTableState(tableId);\n    updateTableState(tableId, {\n      visiblePasswords: {\n        ...currentState.visiblePasswords,\n        [cardId]: !currentState.visiblePasswords[cardId]\n      }\n    });\n  };\n\n  // Handle edit for specific table\n  const handleEdit = (tableId, cardId) => {\n    const currentState = getTableState(tableId);\n    if (currentState.editingRowId === cardId) {\n      // Exit edit mode\n      updateTableState(tableId, {\n        editingRowId: null\n      });\n    } else {\n      // Enter edit mode\n      updateTableState(tableId, {\n        editingRowId: cardId\n      });\n    }\n  };\n\n  // Handle inline edit save - FIXED: Proper backend integration\n  const handleInlineEditSave = async (tableId, cardId, field, value) => {\n    try {\n      var _dataItems$data;\n      const item = dataItems === null || dataItems === void 0 ? void 0 : (_dataItems$data = dataItems.data) === null || _dataItems$data === void 0 ? void 0 : _dataItems$data.find(item => item.id === cardId);\n      if (!item) {\n        toast.error(\"Item not found\");\n        return;\n      }\n\n      // FIXED: Proper data structure for backend\n      let updateData = {\n        password_title: item.password_title || item.title || item.user_name,\n        username: value,\n        // Always use the new value as username\n        password: item.password,\n        department_id: item.department_id,\n        team_id: item.team_id\n      };\n\n      // FIXED: Handle different field updates properly\n      if (field === \"title\") {\n        updateData.password_title = value;\n        updateData.username = item.username; // Keep existing username\n      } else if (field === \"username\") {\n        updateData.username = value;\n        updateData.password_title = item.password_title || item.title || item.user_name; // Keep existing title\n      } else if (field === \"team\") {\n        updateData.team_id = value;\n        updateData.username = item.username;\n        updateData.password_title = item.password_title || item.title || item.user_name;\n      } else if (field === \"department\") {\n        updateData.department_id = value;\n        updateData.username = item.username;\n        updateData.password_title = item.password_title || item.title || item.user_name;\n      }\n      console.log(\"Updating with data:\", updateData); // Debug log\n\n      await updatePasswordManager({\n        id: cardId,\n        ...updateData\n      }).unwrap();\n      refetch();\n      // FIXED: Only show one success notification\n      toast.success(\"Successfully updated!\");\n    } catch (error) {\n      var _error$data;\n      console.error(\"Error updating:\", error);\n      toast.error((error === null || error === void 0 ? void 0 : (_error$data = error.data) === null || _error$data === void 0 ? void 0 : _error$data.message) || \"Failed to update. Please try again.\");\n    }\n  };\n\n  // Handle delete\n  const handleDelete = async cardId => {\n    confirmationAlert({\n      onConfirm: async () => {\n        try {\n          await deletePasswordManager(cardId).unwrap();\n\n          // Remove from all table associations\n          Object.keys(tablePasswordAssociations).forEach(tableId => {\n            removePasswordFromTable(parseInt(tableId), cardId);\n          });\n          refetch();\n          toast.success(\"Password deleted successfully!\");\n        } catch (error) {\n          console.error(\"Error deleting password:\", error);\n          toast.error(\"Failed to delete password. Please try again.\");\n        }\n      }\n    });\n  };\n\n  // Handle successful password creation/update - FIXED: Reset form properly\n  const handlePasswordSuccess = (tableId, newPasswordId = null) => {\n    refetch().then(() => {\n      // If a new password was created, associate it with the table\n      if (newPasswordId) {\n        associatePasswordWithTable(tableId, newPasswordId);\n      }\n    });\n    updateTableState(tableId, {\n      showAddForm: false,\n      editingRowId: null\n    });\n    // FIXED: Only one success notification, no duplicate\n    toast.success(\"Password saved successfully!\");\n  };\n\n  // Toggle shareable card selection\n  const toggleShareableCard = (tableId, cardId) => {\n    const currentState = getTableState(tableId);\n    const currentShareable = currentState.shareableCards;\n    const newShareable = currentShareable.includes(cardId) ? currentShareable.filter(id => id !== cardId) : [...currentShareable, cardId];\n    updateTableState(tableId, {\n      shareableCards: newShareable\n    });\n  };\n\n  // Toggle all shareable cards\n  const toggleAllShareable = tableId => {\n    const currentState = getTableState(tableId);\n    const tableData = (dataItems === null || dataItems === void 0 ? void 0 : dataItems.data) || [];\n    if (currentState.shareableCards.length === tableData.length) {\n      updateTableState(tableId, {\n        shareableCards: []\n      });\n    } else {\n      updateTableState(tableId, {\n        shareableCards: tableData.map(card => card.id)\n      });\n    }\n  };\n\n  // Handle share\n  const handleShare = async tableId => {\n    try {\n      var _userData$teams, _userData$teams$;\n      const currentState = getTableState(tableId);\n      const tableData = (dataItems === null || dataItems === void 0 ? void 0 : dataItems.data) || [];\n      const selectedCards = tableData.filter(card => currentState.shareableCards.includes(card.id));\n      if (selectedCards.length === 0) {\n        toast.error(\"Please select at least one password to share.\");\n        return;\n      }\n      const currentUserTeam = (userData === null || userData === void 0 ? void 0 : (_userData$teams = userData.teams) === null || _userData$teams === void 0 ? void 0 : (_userData$teams$ = _userData$teams[0]) === null || _userData$teams$ === void 0 ? void 0 : _userData$teams$.name) || \"your team\";\n      toast.success(`Successfully shared ${selectedCards.length} password(s) with ${currentUserTeam} members.`);\n      updateTableState(tableId, {\n        shareableCards: []\n      });\n    } catch (error) {\n      console.error(\"Error sharing passwords:\", error);\n      toast.error(\"Failed to share passwords. Please try again.\");\n    }\n  };\n\n  // Handle delete entire table entries\n  const handleDeleteTableEntries = async tableId => {\n    const currentState = getTableState(tableId);\n    if (currentState.shareableCards.length === 0) {\n      toast.error(\"Please select at least one row to delete.\");\n      return;\n    }\n    const tableData = getTableData(tableId);\n    const userOwnedCards = currentState.shareableCards.filter(cardId => {\n      const card = tableData.find(c => c.id === cardId);\n      return card && card.user_id === currentUserId;\n    });\n    if (userOwnedCards.length === 0) {\n      toast.error(\"You can only delete password entries that you created.\");\n      return;\n    }\n    confirmationAlert({\n      onConfirm: async () => {\n        try {\n          await Promise.all(userOwnedCards.map(cardId => deletePasswordManager(cardId).unwrap()));\n\n          // Remove from table associations\n          userOwnedCards.forEach(cardId => {\n            removePasswordFromTable(tableId, cardId);\n          });\n          updateTableState(tableId, {\n            shareableCards: []\n          });\n          refetch();\n          toast.success(`Successfully deleted ${userOwnedCards.length} password entries from this table.`);\n        } catch (error) {\n          console.error(\"Error deleting password entries:\", error);\n          toast.error(\"Failed to delete some password entries. Please try again.\");\n        }\n      }\n    });\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900\",\n    children: tables.map(table => {\n      const tableState = getTableState(table.id);\n      const teamMembers = getTeamMembers();\n      const tableData = getTableData(table.id);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [tableState.editingHeader ? /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: tableState.headerTitle,\n              onChange: e => updateTableState(table.id, {\n                headerTitle: e.target.value\n              }),\n              onBlur: () => updateTableState(table.id, {\n                editingHeader: false\n              }),\n              onKeyDown: e => {\n                if (e.key === \"Enter\") {\n                  updateTableState(table.id, {\n                    editingHeader: false\n                  });\n                }\n              },\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors\",\n              onClick: () => updateTableState(table.id, {\n                editingHeader: true\n              }),\n              title: \"Click to edit table name\",\n              children: tableState.headerTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleShare(table.id),\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              title: \"Share with team\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-sm \",\n                children: \"share\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDeleteTableEntries(table.id),\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              title: \"Delete selected entries\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-sm \",\n                children: \"delete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center -space-x-2 ml-2\",\n              children: [teamMembers.slice(0, 3).map((member, index) => {\n                var _member$fname, _member$lname;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: member !== null && member !== void 0 && member.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: member.photo,\n                    alt: `${member.fname} ${member.lname}`,\n                    className: \"w-8 h-8 rounded-full object-cover border-2 border-white shadow-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm\",\n                    children: [member === null || member === void 0 ? void 0 : (_member$fname = member.fname) === null || _member$fname === void 0 ? void 0 : _member$fname.charAt(0), member === null || member === void 0 ? void 0 : (_member$lname = member.lname) === null || _member$lname === void 0 ? void 0 : _member$lname.charAt(0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this);\n              }), teamMembers.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white shadow-sm\",\n                children: [\"+\", teamMembers.length - 3]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              updateTableState(table.id, {\n                editingRowId: null,\n                showAddForm: !tableState.showAddForm\n              });\n            },\n            className: \"flex items-center justify-center py-2 px-4 text-sm font-medium bg-transparent text-primary border-2 border-primary rounded-full hover:bg-primary hover:text-white transition duration-500 ease-in-out focus:outline-none focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 shadow-sm min-w-[200px] h-[40px]\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), \"Add Password Card\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this), tableState.showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(AddPasswordCardForm, {\n            onCancel: () => {\n              updateTableState(table.id, {\n                showAddForm: false,\n                editingRowId: null\n              });\n            },\n            onSuccess: newPasswordId => handlePasswordSuccess(table.id, newPasswordId),\n            generatedPassword: generatedPassword,\n            passwordStrength: passwordStrength,\n            editData: tableState.editingRowId ? tableData.find(item => item.id === tableState.editingRowId) : null\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg border border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: tableData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mb-4\",\n                children: \"No password cards added yet. Click \\\"Add Password Card\\\" to add your first card.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"w-full text-sm text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50 border-b border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 w-12\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: tableState.shareableCards.length === tableData.length && tableData.length > 0,\n                      onChange: () => toggleAllShareable(table.id),\n                      className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [\"Title\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-3 h-3 ml-1 text-gray-400\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 604,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 599,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [\"User Name\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-3 h-3 ml-1 text-gray-400\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 618,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Team\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Department\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: tableData.map(card => /*#__PURE__*/_jsxDEV(TableRow, {\n                  card: card,\n                  tableId: table.id,\n                  isEditing: tableState.editingRowId === card.id,\n                  isShareable: tableState.shareableCards.includes(card.id),\n                  visiblePassword: tableState.visiblePasswords[card.id],\n                  currentUserId: currentUserId,\n                  onEdit: handleEdit,\n                  onDelete: handleDelete,\n                  onTogglePassword: togglePasswordVisibility,\n                  onToggleShareable: toggleShareableCard,\n                  onInlineEditSave: handleInlineEditSave\n                }, card.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              updateTableState(table.id, {\n                editingRowId: null,\n                showAddForm: true\n              });\n            },\n            className: \"flex items-center gap-3 px-6 py-3 bg-[#006D94] text-white rounded-full shadow-md hover:bg-[#005F80] transition duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 flex items-center justify-center border-2 border-white rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-thin\",\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-medium\",\n              children: \"Add New Password Card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 13\n        }, this)]\n      }, table.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 412,\n    columnNumber: 5\n  }, this);\n};\n\n// TableRow component matching Member Index design\n_s(PasswordCardsTable, \"vDHRIsJvfAByjr70A7jupyyOz9M=\", false, function () {\n  return [useGetPasswordManagerDataQuery, useDeletePasswordManagerMutation, useUpdatePasswordManagerMutation];\n});\n_c = PasswordCardsTable;\nconst TableRow = ({\n  card,\n  tableId,\n  isEditing,\n  isShareable,\n  visiblePassword,\n  currentUserId,\n  onEdit,\n  onDelete,\n  onTogglePassword,\n  onToggleShareable,\n  onInlineEditSave\n}) => {\n  _s2();\n  var _card$team, _card$department, _ref, _ref$charAt, _card$team3, _card$department3;\n  // FIXED: Proper initialization of edit values\n  const [editValues, setEditValues] = useState({\n    title: card.password_title || card.title || card.user_name || \"\",\n    username: card.username || card.user_name || \"\",\n    team: ((_card$team = card.team) === null || _card$team === void 0 ? void 0 : _card$team.name) || \"\",\n    department: ((_card$department = card.department) === null || _card$department === void 0 ? void 0 : _card$department.name) || \"\"\n  });\n  const handleInputChange = (field, value) => {\n    setEditValues(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // FIXED: Proper field value comparison and update\n  const handleInputBlur = field => {\n    var _card$team2, _card$department2;\n    const originalValue = field === \"title\" ? card.password_title || card.title || card.user_name : field === \"username\" ? card.username || card.user_name : field === \"team\" ? (_card$team2 = card.team) === null || _card$team2 === void 0 ? void 0 : _card$team2.name : field === \"department\" ? (_card$department2 = card.department) === null || _card$department2 === void 0 ? void 0 : _card$department2.name : \"\";\n    if (editValues[field] !== originalValue && editValues[field].trim() !== \"\") {\n      onInlineEditSave(tableId, card.id, field, editValues[field]);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === \"Enter\") {\n      e.target.blur();\n    }\n  };\n\n  // Get password strength styling\n  const getPasswordStrengthStyle = level => {\n    switch (level) {\n      case \"Strong\":\n        return \"bg-green-100 text-green-700 border border-green-200\";\n      case \"Moderate\":\n        return \"bg-yellow-100 text-yellow-700 border border-yellow-200\";\n      case \"Weak\":\n        return \"bg-red-100 text-red-700 border border-red-200\";\n      default:\n        return \"bg-green-100 text-green-700 border border-green-200\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"tr\", {\n    className: \"hover:bg-gray-50 transition-colors\",\n    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"checkbox\",\n        checked: isShareable,\n        onChange: () => onToggleShareable(tableId, card.id),\n        className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 787,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center text-white font-medium mr-3 text-sm\",\n          children: ((_ref = card.password_title || card.title || card.user_name) === null || _ref === void 0 ? void 0 : (_ref$charAt = _ref.charAt(0)) === null || _ref$charAt === void 0 ? void 0 : _ref$charAt.toUpperCase()) || \"P\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: editValues.title,\n          onChange: e => handleInputChange(\"title\", e.target.value),\n          onBlur: () => handleInputBlur(\"title\"),\n          onKeyDown: handleKeyDown,\n          className: \"font-medium text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n          autoFocus: true,\n          placeholder: \"Platform/Service Title\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-900\",\n          children: card.password_title || card.title || card.user_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: editValues.username,\n          onChange: e => handleInputChange(\"username\", e.target.value),\n          onBlur: () => handleInputBlur(\"username\"),\n          onKeyDown: handleKeyDown,\n          className: \"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n          placeholder: \"Username or Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-900 mr-2\",\n          children: card.username || card.user_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n          title: \"Copy username\",\n          onClick: () => {\n            const username = card.username || card.user_name;\n            if (username) {\n              navigator.clipboard.writeText(username);\n              toast.success(\"Username copied to clipboard!\");\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-outlined text-lg \",\n            children: \"content_copy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 825,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 824,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-900 mr-2 font-mono\",\n          children: visiblePassword ? card.password : \"••••••••••••\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onTogglePassword(tableId, card.id),\n          className: \"w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n          title: visiblePassword ? \"Hide password\" : \"Show password\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-outlined text-lg\",\n            children: visiblePassword ? \"visibility_off\" : \"visibility\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n          title: \"Copy password\",\n          onClick: () => {\n            if (card.password) {\n              navigator.clipboard.writeText(card.password);\n              toast.success(\"Password copied to clipboard!\");\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-outlined text-lg \",\n            children: \"content_copy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 862,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 861,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: editValues.team,\n        onChange: e => handleInputChange(\"team\", e.target.value),\n        onBlur: () => handleInputBlur(\"team\"),\n        onKeyDown: handleKeyDown,\n        className: \"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-900\",\n        children: ((_card$team3 = card.team) === null || _card$team3 === void 0 ? void 0 : _card$team3.name) || \"Team Name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 895,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: editValues.department,\n        onChange: e => handleInputChange(\"department\", e.target.value),\n        onBlur: () => handleInputBlur(\"department\"),\n        onKeyDown: handleKeyDown,\n        className: \"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-900\",\n        children: ((_card$department3 = card.department) === null || _card$department3 === void 0 ? void 0 : _card$department3.name) || \"Department name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 913,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `px-2 py-1 text-xs font-medium rounded ${getPasswordStrengthStyle(card.level)}`,\n        children: card.level || \"Strong Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 931,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onEdit(tableId, card.id),\n          className: \"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n          title: \"Edit\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-outlined text-base\",\n            children: \"stylus_note\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 946,\n          columnNumber: 11\n        }, this), card.user_id === currentUserId && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onDelete(card.id),\n          className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n          title: \"Delete\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-outlined text-base\",\n            children: \"delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 943,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 942,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 785,\n    columnNumber: 5\n  }, this);\n};\n_s2(TableRow, \"X/HhMSekwCBBSyB1rRfBCxn0FJw=\");\n_c2 = TableRow;\nexport default PasswordCardsTable;\nvar _c, _c2;\n$RefreshReg$(_c, \"PasswordCardsTable\");\n$RefreshReg$(_c2, \"TableRow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AddPasswordCardForm", "<PERSON><PERSON><PERSON><PERSON>", "FetchLoggedInRole", "useGetPasswordManagerDataQuery", "useDeletePasswordManagerMutation", "useUpdatePasswordManagerMutation", "toast", "jsxDEV", "_jsxDEV", "PasswordCardsTable", "generatedPassword", "passwordStrength", "_s", "userData", "currentUserId", "id", "data", "dataItems", "isLoading", "refetch", "sort_by", "order", "page", "per_page", "query", "deletePasswordManager", "updatePasswordManager", "tables", "setTables", "name", "tableStates", "setTableStates", "showAddForm", "editingRowId", "visiblePasswords", "shareableCards", "<PERSON><PERSON><PERSON><PERSON>", "headerTitle", "tablePasswordAssociations", "setTablePasswordAssociations", "initializedExistingPasswords", "setInitializedExistingPasswords", "length", "existingPasswordIds", "map", "item", "prev", "getTableState", "tableId", "updateTableState", "updates", "getTableData", "allData", "tablePasswords", "filter", "includes", "associatePasswordWithTable", "passwordId", "removePasswordFromTable", "handleDeleteTable", "error", "table", "newState", "success", "getTeamMembers", "members", "push", "fname", "lname", "photo", "teams", "for<PERSON>ach", "team", "users", "Array", "isArray", "member", "i", "slice", "togglePasswordVisibility", "cardId", "currentState", "handleEdit", "handleInlineEditSave", "field", "value", "_dataItems$data", "find", "updateData", "password_title", "title", "user_name", "username", "password", "department_id", "team_id", "console", "log", "unwrap", "_error$data", "message", "handleDelete", "onConfirm", "Object", "keys", "parseInt", "handlePasswordSuccess", "newPasswordId", "then", "toggleShareableCard", "currentShareable", "newShareable", "toggleAllShareable", "tableData", "card", "handleShare", "_userData$teams", "_userData$teams$", "selectedCards", "currentUserTeam", "handleDeleteTableEntries", "userOwnedCards", "c", "user_id", "Promise", "all", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tableState", "teamMembers", "type", "onChange", "e", "target", "onBlur", "onKeyDown", "key", "autoFocus", "onClick", "index", "_member$fname", "_member$lname", "src", "alt", "char<PERSON>t", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onCancel", "onSuccess", "editData", "scope", "checked", "fillRule", "clipRule", "TableRow", "isEditing", "isShareable", "visiblePassword", "onEdit", "onDelete", "onTogglePassword", "onToggleShareable", "onInlineEditSave", "_c", "_s2", "_card$team", "_card$department", "_ref", "_ref$charAt", "_card$team3", "_card$department3", "edit<PERSON><PERSON><PERSON>", "setEditV<PERSON>ues", "department", "handleInputChange", "handleInputBlur", "_card$team2", "_card$department2", "originalValue", "trim", "handleKeyDown", "blur", "getPasswordStrengthStyle", "level", "toUpperCase", "placeholder", "navigator", "clipboard", "writeText", "_c2", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport AddPassword<PERSON>ardForm from \"./AddPasswordCardForm\";\r\nimport { confirmationAlert } from \"../../common/coreui\";\r\nimport FetchLoggedInRole from \"../../common/fetchData/FetchLoggedInRole\";\r\nimport {\r\n  useGetPasswordManagerDataQuery,\r\n  useDeletePasswordManagerMutation,\r\n  useUpdatePasswordManagerMutation,\r\n} from \"../../features/api/passwordManagerApi\";\r\nimport { toast } from \"sonner\";\r\n\r\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\r\n  // Get current user data\r\n  const { userData } = FetchLoggedInRole();\r\n  const currentUserId = userData?.id;\r\n\r\n  // API hooks\r\n  const {\r\n    data: dataItems,\r\n    isLoading,\r\n    refetch,\r\n  } = useGetPasswordManagerDataQuery({\r\n    sort_by: \"created_at\",\r\n    order: \"desc\",\r\n    page: 1,\r\n    per_page: 100,\r\n    query: \"\",\r\n  });\r\n\r\n  const [deletePasswordManager] = useDeletePasswordManagerMutation();\r\n  const [updatePasswordManager] = useUpdatePasswordManagerMutation();\r\n\r\n  // State for managing multiple tables\r\n  const [tables, setTables] = useState([\r\n    { id: 1, name: \"Teams Password Card\" },\r\n  ]);\r\n\r\n  // State for each table\r\n  const [tableStates, setTableStates] = useState({\r\n    1: {\r\n      showAddForm: false,\r\n      editingRowId: null,\r\n      visiblePasswords: {},\r\n      shareableCards: [],\r\n      editingHeader: false,\r\n      headerTitle: \"Teams Password Card\",\r\n    },\r\n  });\r\n\r\n  // State to track which passwords belong to which table\r\n  const [tablePasswordAssociations, setTablePasswordAssociations] = useState(\r\n    {}\r\n  );\r\n  const [initializedExistingPasswords, setInitializedExistingPasswords] =\r\n    useState(false);\r\n\r\n  // Initialize existing passwords to the first table\r\n  useEffect(() => {\r\n    if (\r\n      !initializedExistingPasswords &&\r\n      dataItems?.data &&\r\n      dataItems.data.length > 0\r\n    ) {\r\n      const existingPasswordIds = dataItems.data.map((item) => item.id);\r\n      setTablePasswordAssociations((prev) => ({\r\n        ...prev,\r\n        1: existingPasswordIds,\r\n      }));\r\n      setInitializedExistingPasswords(true);\r\n    }\r\n  }, [dataItems, initializedExistingPasswords]);\r\n\r\n  // Get state for specific table\r\n  const getTableState = (tableId) => {\r\n    return (\r\n      tableStates[tableId] || {\r\n        showAddForm: false,\r\n        editingRowId: null,\r\n        visiblePasswords: {},\r\n        shareableCards: [],\r\n        editingHeader: false,\r\n        headerTitle: `Teams Password Card ${tableId}`,\r\n      }\r\n    );\r\n  };\r\n\r\n  // Update state for specific table\r\n  const updateTableState = (tableId, updates) => {\r\n    setTableStates((prev) => ({\r\n      ...prev,\r\n      [tableId]: {\r\n        ...getTableState(tableId),\r\n        ...updates,\r\n      },\r\n    }));\r\n  };\r\n\r\n  // Get passwords for a specific table\r\n  const getTableData = (tableId) => {\r\n    const allData = dataItems?.data || [];\r\n    const tablePasswords = tablePasswordAssociations[tableId] || [];\r\n    return allData.filter((item) => tablePasswords.includes(item.id));\r\n  };\r\n\r\n  // Associate a password with a table\r\n  const associatePasswordWithTable = (tableId, passwordId) => {\r\n    setTablePasswordAssociations((prev) => ({\r\n      ...prev,\r\n      [tableId]: [...(prev[tableId] || []), passwordId],\r\n    }));\r\n  };\r\n\r\n  // Remove password association from table\r\n  const removePasswordFromTable = (tableId, passwordId) => {\r\n    setTablePasswordAssociations((prev) => ({\r\n      ...prev,\r\n      [tableId]: (prev[tableId] || []).filter((id) => id !== passwordId),\r\n    }));\r\n  };\r\n\r\n  // Delete table\r\n  const handleDeleteTable = (tableId) => {\r\n    if (tables.length === 1) {\r\n      toast.error(\r\n        \"Cannot delete the last table. At least one table must remain.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    setTables((prev) => prev.filter((table) => table.id !== tableId));\r\n\r\n    // Remove state for deleted table\r\n    setTableStates((prev) => {\r\n      const newState = { ...prev };\r\n      delete newState[tableId];\r\n      return newState;\r\n    });\r\n\r\n    toast.success(\"Password table deleted!\");\r\n  };\r\n\r\n  // Get team members for avatar display - FIXED: Proper backend integration\r\n  const getTeamMembers = () => {\r\n    const members = [];\r\n\r\n    // Add current user first\r\n    if (userData) {\r\n      members.push({\r\n        id: userData.id,\r\n        fname: userData.fname || \"User\",\r\n        lname: userData.lname || \"\",\r\n        photo: userData.photo || null,\r\n      });\r\n    }\r\n\r\n    // FIXED: Add team members from backend data with proper structure\r\n    if (userData?.teams && userData.teams.length > 0) {\r\n      userData.teams.forEach((team) => {\r\n        // Check if team has users (members) loaded\r\n        if (team.users && Array.isArray(team.users)) {\r\n          team.users.forEach((member) => {\r\n            // Avoid duplicate current user\r\n            if (member.id !== userData.id) {\r\n              members.push({\r\n                id: member.id,\r\n                fname: member.fname || \"Team\",\r\n                lname: member.lname || \"Member\",\r\n                photo: member.photo || null,\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    // If no team members found, add some placeholder members for demo\r\n    if (members.length === 1) {\r\n      for (let i = 2; i <= 4; i++) {\r\n        members.push({\r\n          id: `placeholder_${i}`,\r\n          fname: `Team`,\r\n          lname: `Member ${i}`,\r\n          photo: null,\r\n        });\r\n      }\r\n    }\r\n\r\n    return members.slice(0, 4); // Show max 4 avatars\r\n  };\r\n\r\n  // Toggle password visibility for specific table\r\n  const togglePasswordVisibility = (tableId, cardId) => {\r\n    const currentState = getTableState(tableId);\r\n    updateTableState(tableId, {\r\n      visiblePasswords: {\r\n        ...currentState.visiblePasswords,\r\n        [cardId]: !currentState.visiblePasswords[cardId],\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle edit for specific table\r\n  const handleEdit = (tableId, cardId) => {\r\n    const currentState = getTableState(tableId);\r\n    if (currentState.editingRowId === cardId) {\r\n      // Exit edit mode\r\n      updateTableState(tableId, { editingRowId: null });\r\n    } else {\r\n      // Enter edit mode\r\n      updateTableState(tableId, { editingRowId: cardId });\r\n    }\r\n  };\r\n\r\n  // Handle inline edit save - FIXED: Proper backend integration\r\n  const handleInlineEditSave = async (tableId, cardId, field, value) => {\r\n    try {\r\n      const item = dataItems?.data?.find((item) => item.id === cardId);\r\n      if (!item) {\r\n        toast.error(\"Item not found\");\r\n        return;\r\n      }\r\n\r\n      // FIXED: Proper data structure for backend\r\n      let updateData = {\r\n        password_title: item.password_title || item.title || item.user_name,\r\n        username: value, // Always use the new value as username\r\n        password: item.password,\r\n        department_id: item.department_id,\r\n        team_id: item.team_id,\r\n      };\r\n\r\n      // FIXED: Handle different field updates properly\r\n      if (field === \"title\") {\r\n        updateData.password_title = value;\r\n        updateData.username = item.username; // Keep existing username\r\n      } else if (field === \"username\") {\r\n        updateData.username = value;\r\n        updateData.password_title =\r\n          item.password_title || item.title || item.user_name; // Keep existing title\r\n      } else if (field === \"team\") {\r\n        updateData.team_id = value;\r\n        updateData.username = item.username;\r\n        updateData.password_title =\r\n          item.password_title || item.title || item.user_name;\r\n      } else if (field === \"department\") {\r\n        updateData.department_id = value;\r\n        updateData.username = item.username;\r\n        updateData.password_title =\r\n          item.password_title || item.title || item.user_name;\r\n      }\r\n\r\n      console.log(\"Updating with data:\", updateData); // Debug log\r\n\r\n      await updatePasswordManager({ id: cardId, ...updateData }).unwrap();\r\n      refetch();\r\n      // FIXED: Only show one success notification\r\n      toast.success(\"Successfully updated!\");\r\n    } catch (error) {\r\n      console.error(\"Error updating:\", error);\r\n      toast.error(\r\n        error?.data?.message || \"Failed to update. Please try again.\"\r\n      );\r\n    }\r\n  };\r\n\r\n  // Handle delete\r\n  const handleDelete = async (cardId) => {\r\n    confirmationAlert({\r\n      onConfirm: async () => {\r\n        try {\r\n          await deletePasswordManager(cardId).unwrap();\r\n\r\n          // Remove from all table associations\r\n          Object.keys(tablePasswordAssociations).forEach((tableId) => {\r\n            removePasswordFromTable(parseInt(tableId), cardId);\r\n          });\r\n\r\n          refetch();\r\n          toast.success(\"Password deleted successfully!\");\r\n        } catch (error) {\r\n          console.error(\"Error deleting password:\", error);\r\n          toast.error(\"Failed to delete password. Please try again.\");\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle successful password creation/update - FIXED: Reset form properly\r\n  const handlePasswordSuccess = (tableId, newPasswordId = null) => {\r\n    refetch().then(() => {\r\n      // If a new password was created, associate it with the table\r\n      if (newPasswordId) {\r\n        associatePasswordWithTable(tableId, newPasswordId);\r\n      }\r\n    });\r\n    updateTableState(tableId, {\r\n      showAddForm: false,\r\n      editingRowId: null,\r\n    });\r\n    // FIXED: Only one success notification, no duplicate\r\n    toast.success(\"Password saved successfully!\");\r\n  };\r\n\r\n  // Toggle shareable card selection\r\n  const toggleShareableCard = (tableId, cardId) => {\r\n    const currentState = getTableState(tableId);\r\n    const currentShareable = currentState.shareableCards;\r\n    const newShareable = currentShareable.includes(cardId)\r\n      ? currentShareable.filter((id) => id !== cardId)\r\n      : [...currentShareable, cardId];\r\n\r\n    updateTableState(tableId, { shareableCards: newShareable });\r\n  };\r\n\r\n  // Toggle all shareable cards\r\n  const toggleAllShareable = (tableId) => {\r\n    const currentState = getTableState(tableId);\r\n    const tableData = dataItems?.data || [];\r\n\r\n    if (currentState.shareableCards.length === tableData.length) {\r\n      updateTableState(tableId, { shareableCards: [] });\r\n    } else {\r\n      updateTableState(tableId, {\r\n        shareableCards: tableData.map((card) => card.id),\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle share\r\n  const handleShare = async (tableId) => {\r\n    try {\r\n      const currentState = getTableState(tableId);\r\n      const tableData = dataItems?.data || [];\r\n      const selectedCards = tableData.filter((card) =>\r\n        currentState.shareableCards.includes(card.id)\r\n      );\r\n\r\n      if (selectedCards.length === 0) {\r\n        toast.error(\"Please select at least one password to share.\");\r\n        return;\r\n      }\r\n\r\n      const currentUserTeam = userData?.teams?.[0]?.name || \"your team\";\r\n      toast.success(\r\n        `Successfully shared ${selectedCards.length} password(s) with ${currentUserTeam} members.`\r\n      );\r\n      updateTableState(tableId, { shareableCards: [] });\r\n    } catch (error) {\r\n      console.error(\"Error sharing passwords:\", error);\r\n      toast.error(\"Failed to share passwords. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Handle delete entire table entries\r\n  const handleDeleteTableEntries = async (tableId) => {\r\n    const currentState = getTableState(tableId);\r\n\r\n    if (currentState.shareableCards.length === 0) {\r\n      toast.error(\"Please select at least one row to delete.\");\r\n      return;\r\n    }\r\n\r\n    const tableData = getTableData(tableId);\r\n    const userOwnedCards = currentState.shareableCards.filter((cardId) => {\r\n      const card = tableData.find((c) => c.id === cardId);\r\n      return card && card.user_id === currentUserId;\r\n    });\r\n\r\n    if (userOwnedCards.length === 0) {\r\n      toast.error(\"You can only delete password entries that you created.\");\r\n      return;\r\n    }\r\n\r\n    confirmationAlert({\r\n      onConfirm: async () => {\r\n        try {\r\n          await Promise.all(\r\n            userOwnedCards.map((cardId) =>\r\n              deletePasswordManager(cardId).unwrap()\r\n            )\r\n          );\r\n\r\n          // Remove from table associations\r\n          userOwnedCards.forEach((cardId) => {\r\n            removePasswordFromTable(tableId, cardId);\r\n          });\r\n\r\n          updateTableState(tableId, { shareableCards: [] });\r\n          refetch();\r\n          toast.success(\r\n            `Successfully deleted ${userOwnedCards.length} password entries from this table.`\r\n          );\r\n        } catch (error) {\r\n          console.error(\"Error deleting password entries:\", error);\r\n          toast.error(\r\n            \"Failed to delete some password entries. Please try again.\"\r\n          );\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center py-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900\">\r\n      {/* Render all tables */}\r\n      {tables.map((table) => {\r\n        const tableState = getTableState(table.id);\r\n        const teamMembers = getTeamMembers();\r\n        const tableData = getTableData(table.id);\r\n\r\n        return (\r\n          <div key={table.id} className=\"mb-8\">\r\n            {/* Table Header - Matching Member Index Design */}\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                {/* Editable Table Title */}\r\n                {tableState.editingHeader ? (\r\n                  <input\r\n                    type=\"text\"\r\n                    value={tableState.headerTitle}\r\n                    onChange={(e) =>\r\n                      updateTableState(table.id, {\r\n                        headerTitle: e.target.value,\r\n                      })\r\n                    }\r\n                    onBlur={() =>\r\n                      updateTableState(table.id, { editingHeader: false })\r\n                    }\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === \"Enter\") {\r\n                        updateTableState(table.id, { editingHeader: false });\r\n                      }\r\n                    }}\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    autoFocus\r\n                  />\r\n                ) : (\r\n                  <h2\r\n                    className=\"text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors\"\r\n                    onClick={() =>\r\n                      updateTableState(table.id, { editingHeader: true })\r\n                    }\r\n                    title=\"Click to edit table name\"\r\n                  >\r\n                    {tableState.headerTitle}\r\n                  </h2>\r\n                )}\r\n\r\n                {/* Share Icon */}\r\n                <button\r\n                  onClick={() => handleShare(table.id)}\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  title=\"Share with team\"\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm \">\r\n                    share\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Delete Selected Icon */}\r\n                <button\r\n                  onClick={() => handleDeleteTableEntries(table.id)}\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  title=\"Delete selected entries\"\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm \">\r\n                    delete\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Delete Entire Table */}\r\n                {/* <button\r\n                  onClick={() => handleDeleteTable(table.id)}\r\n                  className=\"text-gray-400 hover:text-red-600 transition-colors p-1\"\r\n                  title=\"Delete entire table\"\r\n                >\r\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"/>\r\n                  </svg>\r\n                </button> */}\r\n\r\n                {/* Team Member Avatars - Placeholder */}\r\n                <div className=\"flex items-center -space-x-2 ml-2\">\r\n                  {teamMembers.slice(0, 3).map((member, index) => (\r\n                    <div key={index} className=\"relative\">\r\n                      {member?.photo ? (\r\n                        <img\r\n                          src={member.photo}\r\n                          alt={`${member.fname} ${member.lname}`}\r\n                          className=\"w-8 h-8 rounded-full object-cover border-2 border-white shadow-sm\"\r\n                        />\r\n                      ) : (\r\n                        <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm\">\r\n                          {member?.fname?.charAt(0)}\r\n                          {member?.lname?.charAt(0)}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                  {teamMembers.length > 3 && (\r\n                    <div className=\"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white shadow-sm\">\r\n                      +{teamMembers.length - 3}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <button\r\n                onClick={() => {\r\n                  updateTableState(table.id, {\r\n                    editingRowId: null,\r\n                    showAddForm: !tableState.showAddForm,\r\n                  });\r\n                }}\r\n                className=\"flex items-center justify-center py-2 px-4 text-sm font-medium bg-transparent text-primary border-2 border-primary rounded-full hover:bg-primary hover:text-white transition duration-500 ease-in-out focus:outline-none focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 shadow-sm min-w-[200px] h-[40px]\"\r\n              >\r\n                <svg\r\n                  className=\"w-4 h-4 mr-2\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"\r\n                  />\r\n                </svg>\r\n                Add Password Card\r\n              </button>\r\n            </div>\r\n\r\n            {/* Add Password Form */}\r\n            {tableState.showAddForm && (\r\n              <div className=\"mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200\">\r\n                <AddPasswordCardForm\r\n                  onCancel={() => {\r\n                    updateTableState(table.id, {\r\n                      showAddForm: false,\r\n                      editingRowId: null,\r\n                    });\r\n                  }}\r\n                  onSuccess={(newPasswordId) =>\r\n                    handlePasswordSuccess(table.id, newPasswordId)\r\n                  }\r\n                  generatedPassword={generatedPassword}\r\n                  passwordStrength={passwordStrength}\r\n                  editData={\r\n                    tableState.editingRowId\r\n                      ? tableData.find(\r\n                          (item) => item.id === tableState.editingRowId\r\n                        )\r\n                      : null\r\n                  }\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* Table - Matching Member Index Design */}\r\n            <div className=\"bg-white rounded-lg border border-gray-200\">\r\n              <div className=\"overflow-x-auto\">\r\n                {tableData.length === 0 ? (\r\n                  <div className=\"text-center py-12\">\r\n                    <p className=\"text-gray-500 mb-4\">\r\n                      No password cards added yet. Click \"Add Password Card\" to\r\n                      add your first card.\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  <table className=\"w-full text-sm text-left\">\r\n                    <thead className=\"bg-gray-50 border-b border-gray-200\">\r\n                      <tr>\r\n                        <th scope=\"col\" className=\"px-4 py-3 w-12\">\r\n                          <input\r\n                            type=\"checkbox\"\r\n                            checked={\r\n                              tableState.shareableCards.length ===\r\n                                tableData.length && tableData.length > 0\r\n                            }\r\n                            onChange={() => toggleAllShareable(table.id)}\r\n                            className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\r\n                          />\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\r\n                        >\r\n                          <div className=\"flex items-center\">\r\n                            Title\r\n                            <svg\r\n                              className=\"w-3 h-3 ml-1 text-gray-400\"\r\n                              fill=\"currentColor\"\r\n                              viewBox=\"0 0 20 20\"\r\n                            >\r\n                              <path\r\n                                fillRule=\"evenodd\"\r\n                                d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n                                clipRule=\"evenodd\"\r\n                              />\r\n                            </svg>\r\n                          </div>\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\r\n                        >\r\n                          <div className=\"flex items-center\">\r\n                            User Name\r\n                            <svg\r\n                              className=\"w-3 h-3 ml-1 text-gray-400\"\r\n                              fill=\"currentColor\"\r\n                              viewBox=\"0 0 20 20\"\r\n                            >\r\n                              <path\r\n                                fillRule=\"evenodd\"\r\n                                d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n                                clipRule=\"evenodd\"\r\n                              />\r\n                            </svg>\r\n                          </div>\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\r\n                        >\r\n                          Password\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\r\n                        >\r\n                          Team\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\r\n                        >\r\n                          Department\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\r\n                        >\r\n                          Level\r\n                        </th>\r\n                        <th\r\n                          scope=\"col\"\r\n                          className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\r\n                        >\r\n                          Action\r\n                        </th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                      {tableData.map((card) => (\r\n                        <TableRow\r\n                          key={card.id}\r\n                          card={card}\r\n                          tableId={table.id}\r\n                          isEditing={tableState.editingRowId === card.id}\r\n                          isShareable={tableState.shareableCards.includes(\r\n                            card.id\r\n                          )}\r\n                          visiblePassword={tableState.visiblePasswords[card.id]}\r\n                          currentUserId={currentUserId}\r\n                          onEdit={handleEdit}\r\n                          onDelete={handleDelete}\r\n                          onTogglePassword={togglePasswordVisibility}\r\n                          onToggleShareable={toggleShareableCard}\r\n                          onInlineEditSave={handleInlineEditSave}\r\n                        />\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* FIXED: Add New Password Card Button - Bottom Center for Each Table */}\r\n            <div className=\"flex justify-center mt-6\">\r\n              <button\r\n                onClick={() => {\r\n                  updateTableState(table.id, {\r\n                    editingRowId: null,\r\n                    showAddForm: true,\r\n                  });\r\n                }}\r\n                className=\"flex items-center gap-3 px-6 py-3 bg-[#006D94] text-white rounded-full shadow-md hover:bg-[#005F80] transition duration-200\"\r\n              >\r\n                {/* Circular Plus Icon */}\r\n                <div className=\"w-8 h-8 flex items-center justify-center border-2 border-white rounded-full\">\r\n                  <span className=\"text-lg font-thin\">+</span>\r\n                </div>\r\n\r\n                {/* Button Text */}\r\n                <span className=\"text-lg font-medium\">\r\n                  Add New Password Card\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\n// TableRow component matching Member Index design\r\nconst TableRow = ({\r\n  card,\r\n  tableId,\r\n  isEditing,\r\n  isShareable,\r\n  visiblePassword,\r\n  currentUserId,\r\n  onEdit,\r\n  onDelete,\r\n  onTogglePassword,\r\n  onToggleShareable,\r\n  onInlineEditSave,\r\n}) => {\r\n  // FIXED: Proper initialization of edit values\r\n  const [editValues, setEditValues] = useState({\r\n    title: card.password_title || card.title || card.user_name || \"\",\r\n    username: card.username || card.user_name || \"\",\r\n    team: card.team?.name || \"\",\r\n    department: card.department?.name || \"\",\r\n  });\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setEditValues((prev) => ({ ...prev, [field]: value }));\r\n  };\r\n\r\n  // FIXED: Proper field value comparison and update\r\n  const handleInputBlur = (field) => {\r\n    const originalValue =\r\n      field === \"title\"\r\n        ? card.password_title || card.title || card.user_name\r\n        : field === \"username\"\r\n        ? card.username || card.user_name\r\n        : field === \"team\"\r\n        ? card.team?.name\r\n        : field === \"department\"\r\n        ? card.department?.name\r\n        : \"\";\r\n\r\n    if (\r\n      editValues[field] !== originalValue &&\r\n      editValues[field].trim() !== \"\"\r\n    ) {\r\n      onInlineEditSave(tableId, card.id, field, editValues[field]);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === \"Enter\") {\r\n      e.target.blur();\r\n    }\r\n  };\r\n\r\n  // Get password strength styling\r\n  const getPasswordStrengthStyle = (level) => {\r\n    switch (level) {\r\n      case \"Strong\":\r\n        return \"bg-green-100 text-green-700 border border-green-200\";\r\n      case \"Moderate\":\r\n        return \"bg-yellow-100 text-yellow-700 border border-yellow-200\";\r\n      case \"Weak\":\r\n        return \"bg-red-100 text-red-700 border border-red-200\";\r\n      default:\r\n        return \"bg-green-100 text-green-700 border border-green-200\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <tr className=\"hover:bg-gray-50 transition-colors\">\r\n      <td className=\"px-4 py-4\">\r\n        <input\r\n          type=\"checkbox\"\r\n          checked={isShareable}\r\n          onChange={() => onToggleShareable(tableId, card.id)}\r\n          className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\r\n        />\r\n      </td>\r\n\r\n      {/* Title Column */}\r\n      <td className=\"px-4 py-4\">\r\n        <div className=\"flex items-center\">\r\n          {/* Platform Icon Circle - Matching your design */}\r\n          <div className=\"w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center text-white font-medium mr-3 text-sm\">\r\n            {(card.password_title || card.title || card.user_name)\r\n              ?.charAt(0)\r\n              ?.toUpperCase() || \"P\"}\r\n          </div>\r\n          {isEditing ? (\r\n            <input\r\n              type=\"text\"\r\n              value={editValues.title}\r\n              onChange={(e) => handleInputChange(\"title\", e.target.value)}\r\n              onBlur={() => handleInputBlur(\"title\")}\r\n              onKeyDown={handleKeyDown}\r\n              className=\"font-medium text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n              autoFocus\r\n              placeholder=\"Platform/Service Title\"\r\n            />\r\n          ) : (\r\n            <span className=\"font-medium text-gray-900\">\r\n              {card.password_title || card.title || card.user_name}\r\n            </span>\r\n          )}\r\n        </div>\r\n      </td>\r\n\r\n      {/* User Name Column */}\r\n      <td className=\"px-4 py-4\">\r\n        <div className=\"flex items-center\">\r\n          {isEditing ? (\r\n            <input\r\n              type=\"text\"\r\n              value={editValues.username}\r\n              onChange={(e) => handleInputChange(\"username\", e.target.value)}\r\n              onBlur={() => handleInputBlur(\"username\")}\r\n              onKeyDown={handleKeyDown}\r\n              className=\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full mr-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n              placeholder=\"Username or Email\"\r\n            />\r\n          ) : (\r\n            <span className=\"text-gray-900 mr-2\">\r\n              {card.username || card.user_name}\r\n            </span>\r\n          )}\r\n          {/* Copy Username Icon */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            title=\"Copy username\"\r\n            onClick={() => {\r\n              const username = card.username || card.user_name;\r\n              if (username) {\r\n                navigator.clipboard.writeText(username);\r\n                toast.success(\"Username copied to clipboard!\");\r\n              }\r\n            }}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg \">\r\n              content_copy\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </td>\r\n\r\n      {/* Password Column */}\r\n      <td className=\"px-4 py-4\">\r\n        <div className=\"flex items-center\">\r\n          <span className=\"text-gray-900 mr-2 font-mono\">\r\n            {visiblePassword ? card.password : \"••••••••••••\"}\r\n          </span>\r\n          {/* Eye Icon (using Material Symbols) */}\r\n          <button\r\n            onClick={() => onTogglePassword(tableId, card.id)}\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            title={visiblePassword ? \"Hide password\" : \"Show password\"}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">\r\n              {visiblePassword ? \"visibility_off\" : \"visibility\"}\r\n            </span>\r\n          </button>\r\n          {/* Copy Password Icon */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            title=\"Copy password\"\r\n            onClick={() => {\r\n              if (card.password) {\r\n                navigator.clipboard.writeText(card.password);\r\n                toast.success(\"Password copied to clipboard!\");\r\n              }\r\n            }}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg \">\r\n              content_copy\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </td>\r\n\r\n      {/* Team Column */}\r\n      <td className=\"px-4 py-4\">\r\n        {isEditing ? (\r\n          <input\r\n            type=\"text\"\r\n            value={editValues.team}\r\n            onChange={(e) => handleInputChange(\"team\", e.target.value)}\r\n            onBlur={() => handleInputBlur(\"team\")}\r\n            onKeyDown={handleKeyDown}\r\n            className=\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n          />\r\n        ) : (\r\n          <span className=\"text-gray-900\">\r\n            {card.team?.name || \"Team Name\"}\r\n          </span>\r\n        )}\r\n      </td>\r\n\r\n      {/* Department Column */}\r\n      <td className=\"px-4 py-4\">\r\n        {isEditing ? (\r\n          <input\r\n            type=\"text\"\r\n            value={editValues.department}\r\n            onChange={(e) => handleInputChange(\"department\", e.target.value)}\r\n            onBlur={() => handleInputBlur(\"department\")}\r\n            onKeyDown={handleKeyDown}\r\n            className=\"text-gray-900 border border-gray-300 rounded px-2 py-1 w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n          />\r\n        ) : (\r\n          <span className=\"text-gray-900\">\r\n            {card.department?.name || \"Department name\"}\r\n          </span>\r\n        )}\r\n      </td>\r\n\r\n      {/* Level Column - Matching your design colors */}\r\n      <td className=\"px-4 py-4\">\r\n        <span\r\n          className={`px-2 py-1 text-xs font-medium rounded ${getPasswordStrengthStyle(\r\n            card.level\r\n          )}`}\r\n        >\r\n          {card.level || \"Strong Password\"}\r\n        </span>\r\n      </td>\r\n\r\n      {/* Action Column - Matching Member Index design */}\r\n      <td className=\"px-4 py-4\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {/* Edit Icon - Matching your design */}\r\n          {/* Edit Button */}\r\n          <button\r\n            onClick={() => onEdit(tableId, card.id)}\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            title=\"Edit\"\r\n          >\r\n            <span className=\"material-symbols-outlined text-base\">\r\n              stylus_note\r\n            </span>\r\n          </button>\r\n\r\n          {/* Delete Icon - Only for card owner */}\r\n          {card.user_id === currentUserId && (\r\n            <button\r\n              onClick={() => onDelete(card.id)}\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              title=\"Delete\"\r\n            >\r\n              <span className=\"material-symbols-outlined text-base\">\r\n                delete\r\n              </span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      </td>\r\n    </tr>\r\n  );\r\n};\r\n\r\nexport default PasswordCardsTable;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,SACEC,8BAA8B,EAC9BC,gCAAgC,EAChCC,gCAAgC,QAC3B,uCAAuC;AAC9C,SAASC,KAAK,QAAQ,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACtE;EACA,MAAM;IAAEC;EAAS,CAAC,GAAGX,iBAAiB,CAAC,CAAC;EACxC,MAAMY,aAAa,GAAGD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,EAAE;;EAElC;EACA,MAAM;IACJC,IAAI,EAAEC,SAAS;IACfC,SAAS;IACTC;EACF,CAAC,GAAGhB,8BAA8B,CAAC;IACjCiB,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,qBAAqB,CAAC,GAAGrB,gCAAgC,CAAC,CAAC;EAClE,MAAM,CAACsB,qBAAqB,CAAC,GAAGrB,gCAAgC,CAAC,CAAC;;EAElE;EACA,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAAC,CACnC;IAAEiB,EAAE,EAAE,CAAC;IAAEc,IAAI,EAAE;EAAsB,CAAC,CACvC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC;IAC7C,CAAC,EAAE;MACDkC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE,CAAC,CAAC;MACpBC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,KAAK;MACpBC,WAAW,EAAE;IACf;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGzC,QAAQ,CACxE,CAAC,CACH,CAAC;EACD,MAAM,CAAC0C,4BAA4B,EAAEC,+BAA+B,CAAC,GACnE3C,QAAQ,CAAC,KAAK,CAAC;;EAEjB;EACAC,SAAS,CAAC,MAAM;IACd,IACE,CAACyC,4BAA4B,IAC7BvB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAED,IAAI,IACfC,SAAS,CAACD,IAAI,CAAC0B,MAAM,GAAG,CAAC,EACzB;MACA,MAAMC,mBAAmB,GAAG1B,SAAS,CAACD,IAAI,CAAC4B,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAC9B,EAAE,CAAC;MACjEwB,4BAA4B,CAAEO,IAAI,KAAM;QACtC,GAAGA,IAAI;QACP,CAAC,EAAEH;MACL,CAAC,CAAC,CAAC;MACHF,+BAA+B,CAAC,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACxB,SAAS,EAAEuB,4BAA4B,CAAC,CAAC;;EAE7C;EACA,MAAMO,aAAa,GAAIC,OAAO,IAAK;IACjC,OACElB,WAAW,CAACkB,OAAO,CAAC,IAAI;MACtBhB,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE,CAAC,CAAC;MACpBC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,KAAK;MACpBC,WAAW,EAAE,uBAAuBW,OAAO;IAC7C,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACD,OAAO,EAAEE,OAAO,KAAK;IAC7CnB,cAAc,CAAEe,IAAI,KAAM;MACxB,GAAGA,IAAI;MACP,CAACE,OAAO,GAAG;QACT,GAAGD,aAAa,CAACC,OAAO,CAAC;QACzB,GAAGE;MACL;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIH,OAAO,IAAK;IAChC,MAAMI,OAAO,GAAG,CAAAnC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAED,IAAI,KAAI,EAAE;IACrC,MAAMqC,cAAc,GAAGf,yBAAyB,CAACU,OAAO,CAAC,IAAI,EAAE;IAC/D,OAAOI,OAAO,CAACE,MAAM,CAAET,IAAI,IAAKQ,cAAc,CAACE,QAAQ,CAACV,IAAI,CAAC9B,EAAE,CAAC,CAAC;EACnE,CAAC;;EAED;EACA,MAAMyC,0BAA0B,GAAGA,CAACR,OAAO,EAAES,UAAU,KAAK;IAC1DlB,4BAA4B,CAAEO,IAAI,KAAM;MACtC,GAAGA,IAAI;MACP,CAACE,OAAO,GAAG,CAAC,IAAIF,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,CAAC,EAAES,UAAU;IAClD,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAACV,OAAO,EAAES,UAAU,KAAK;IACvDlB,4BAA4B,CAAEO,IAAI,KAAM;MACtC,GAAGA,IAAI;MACP,CAACE,OAAO,GAAG,CAACF,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,EAAEM,MAAM,CAAEvC,EAAE,IAAKA,EAAE,KAAK0C,UAAU;IACnE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIX,OAAO,IAAK;IACrC,IAAIrB,MAAM,CAACe,MAAM,KAAK,CAAC,EAAE;MACvBpC,KAAK,CAACsD,KAAK,CACT,+DACF,CAAC;MACD;IACF;IAEAhC,SAAS,CAAEkB,IAAI,IAAKA,IAAI,CAACQ,MAAM,CAAEO,KAAK,IAAKA,KAAK,CAAC9C,EAAE,KAAKiC,OAAO,CAAC,CAAC;;IAEjE;IACAjB,cAAc,CAAEe,IAAI,IAAK;MACvB,MAAMgB,QAAQ,GAAG;QAAE,GAAGhB;MAAK,CAAC;MAC5B,OAAOgB,QAAQ,CAACd,OAAO,CAAC;MACxB,OAAOc,QAAQ;IACjB,CAAC,CAAC;IAEFxD,KAAK,CAACyD,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAO,GAAG,EAAE;;IAElB;IACA,IAAIpD,QAAQ,EAAE;MACZoD,OAAO,CAACC,IAAI,CAAC;QACXnD,EAAE,EAAEF,QAAQ,CAACE,EAAE;QACfoD,KAAK,EAAEtD,QAAQ,CAACsD,KAAK,IAAI,MAAM;QAC/BC,KAAK,EAAEvD,QAAQ,CAACuD,KAAK,IAAI,EAAE;QAC3BC,KAAK,EAAExD,QAAQ,CAACwD,KAAK,IAAI;MAC3B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIxD,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEyD,KAAK,IAAIzD,QAAQ,CAACyD,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAAE;MAChD7B,QAAQ,CAACyD,KAAK,CAACC,OAAO,CAAEC,IAAI,IAAK;QAC/B;QACA,IAAIA,IAAI,CAACC,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAACC,KAAK,CAAC,EAAE;UAC3CD,IAAI,CAACC,KAAK,CAACF,OAAO,CAAEK,MAAM,IAAK;YAC7B;YACA,IAAIA,MAAM,CAAC7D,EAAE,KAAKF,QAAQ,CAACE,EAAE,EAAE;cAC7BkD,OAAO,CAACC,IAAI,CAAC;gBACXnD,EAAE,EAAE6D,MAAM,CAAC7D,EAAE;gBACboD,KAAK,EAAES,MAAM,CAACT,KAAK,IAAI,MAAM;gBAC7BC,KAAK,EAAEQ,MAAM,CAACR,KAAK,IAAI,QAAQ;gBAC/BC,KAAK,EAAEO,MAAM,CAACP,KAAK,IAAI;cACzB,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIJ,OAAO,CAACvB,MAAM,KAAK,CAAC,EAAE;MACxB,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3BZ,OAAO,CAACC,IAAI,CAAC;UACXnD,EAAE,EAAE,eAAe8D,CAAC,EAAE;UACtBV,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE,UAAUS,CAAC,EAAE;UACpBR,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;IAEA,OAAOJ,OAAO,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAC/B,OAAO,EAAEgC,MAAM,KAAK;IACpD,MAAMC,YAAY,GAAGlC,aAAa,CAACC,OAAO,CAAC;IAC3CC,gBAAgB,CAACD,OAAO,EAAE;MACxBd,gBAAgB,EAAE;QAChB,GAAG+C,YAAY,CAAC/C,gBAAgB;QAChC,CAAC8C,MAAM,GAAG,CAACC,YAAY,CAAC/C,gBAAgB,CAAC8C,MAAM;MACjD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAClC,OAAO,EAAEgC,MAAM,KAAK;IACtC,MAAMC,YAAY,GAAGlC,aAAa,CAACC,OAAO,CAAC;IAC3C,IAAIiC,YAAY,CAAChD,YAAY,KAAK+C,MAAM,EAAE;MACxC;MACA/B,gBAAgB,CAACD,OAAO,EAAE;QAAEf,YAAY,EAAE;MAAK,CAAC,CAAC;IACnD,CAAC,MAAM;MACL;MACAgB,gBAAgB,CAACD,OAAO,EAAE;QAAEf,YAAY,EAAE+C;MAAO,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAG,MAAAA,CAAOnC,OAAO,EAAEgC,MAAM,EAAEI,KAAK,EAAEC,KAAK,KAAK;IACpE,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMzC,IAAI,GAAG5B,SAAS,aAATA,SAAS,wBAAAqE,eAAA,GAATrE,SAAS,CAAED,IAAI,cAAAsE,eAAA,uBAAfA,eAAA,CAAiBC,IAAI,CAAE1C,IAAI,IAAKA,IAAI,CAAC9B,EAAE,KAAKiE,MAAM,CAAC;MAChE,IAAI,CAACnC,IAAI,EAAE;QACTvC,KAAK,CAACsD,KAAK,CAAC,gBAAgB,CAAC;QAC7B;MACF;;MAEA;MACA,IAAI4B,UAAU,GAAG;QACfC,cAAc,EAAE5C,IAAI,CAAC4C,cAAc,IAAI5C,IAAI,CAAC6C,KAAK,IAAI7C,IAAI,CAAC8C,SAAS;QACnEC,QAAQ,EAAEP,KAAK;QAAE;QACjBQ,QAAQ,EAAEhD,IAAI,CAACgD,QAAQ;QACvBC,aAAa,EAAEjD,IAAI,CAACiD,aAAa;QACjCC,OAAO,EAAElD,IAAI,CAACkD;MAChB,CAAC;;MAED;MACA,IAAIX,KAAK,KAAK,OAAO,EAAE;QACrBI,UAAU,CAACC,cAAc,GAAGJ,KAAK;QACjCG,UAAU,CAACI,QAAQ,GAAG/C,IAAI,CAAC+C,QAAQ,CAAC,CAAC;MACvC,CAAC,MAAM,IAAIR,KAAK,KAAK,UAAU,EAAE;QAC/BI,UAAU,CAACI,QAAQ,GAAGP,KAAK;QAC3BG,UAAU,CAACC,cAAc,GACvB5C,IAAI,CAAC4C,cAAc,IAAI5C,IAAI,CAAC6C,KAAK,IAAI7C,IAAI,CAAC8C,SAAS,CAAC,CAAC;MACzD,CAAC,MAAM,IAAIP,KAAK,KAAK,MAAM,EAAE;QAC3BI,UAAU,CAACO,OAAO,GAAGV,KAAK;QAC1BG,UAAU,CAACI,QAAQ,GAAG/C,IAAI,CAAC+C,QAAQ;QACnCJ,UAAU,CAACC,cAAc,GACvB5C,IAAI,CAAC4C,cAAc,IAAI5C,IAAI,CAAC6C,KAAK,IAAI7C,IAAI,CAAC8C,SAAS;MACvD,CAAC,MAAM,IAAIP,KAAK,KAAK,YAAY,EAAE;QACjCI,UAAU,CAACM,aAAa,GAAGT,KAAK;QAChCG,UAAU,CAACI,QAAQ,GAAG/C,IAAI,CAAC+C,QAAQ;QACnCJ,UAAU,CAACC,cAAc,GACvB5C,IAAI,CAAC4C,cAAc,IAAI5C,IAAI,CAAC6C,KAAK,IAAI7C,IAAI,CAAC8C,SAAS;MACvD;MAEAK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAET,UAAU,CAAC,CAAC,CAAC;;MAEhD,MAAM9D,qBAAqB,CAAC;QAAEX,EAAE,EAAEiE,MAAM;QAAE,GAAGQ;MAAW,CAAC,CAAC,CAACU,MAAM,CAAC,CAAC;MACnE/E,OAAO,CAAC,CAAC;MACT;MACAb,KAAK,CAACyD,OAAO,CAAC,uBAAuB,CAAC;IACxC,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAAuC,WAAA;MACdH,OAAO,CAACpC,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCtD,KAAK,CAACsD,KAAK,CACT,CAAAA,KAAK,aAALA,KAAK,wBAAAuC,WAAA,GAALvC,KAAK,CAAE5C,IAAI,cAAAmF,WAAA,uBAAXA,WAAA,CAAaC,OAAO,KAAI,qCAC1B,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAOrB,MAAM,IAAK;IACrC/E,iBAAiB,CAAC;MAChBqG,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAI;UACF,MAAM7E,qBAAqB,CAACuD,MAAM,CAAC,CAACkB,MAAM,CAAC,CAAC;;UAE5C;UACAK,MAAM,CAACC,IAAI,CAAClE,yBAAyB,CAAC,CAACiC,OAAO,CAAEvB,OAAO,IAAK;YAC1DU,uBAAuB,CAAC+C,QAAQ,CAACzD,OAAO,CAAC,EAAEgC,MAAM,CAAC;UACpD,CAAC,CAAC;UAEF7D,OAAO,CAAC,CAAC;UACTb,KAAK,CAACyD,OAAO,CAAC,gCAAgC,CAAC;QACjD,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdoC,OAAO,CAACpC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChDtD,KAAK,CAACsD,KAAK,CAAC,8CAA8C,CAAC;QAC7D;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8C,qBAAqB,GAAGA,CAAC1D,OAAO,EAAE2D,aAAa,GAAG,IAAI,KAAK;IAC/DxF,OAAO,CAAC,CAAC,CAACyF,IAAI,CAAC,MAAM;MACnB;MACA,IAAID,aAAa,EAAE;QACjBnD,0BAA0B,CAACR,OAAO,EAAE2D,aAAa,CAAC;MACpD;IACF,CAAC,CAAC;IACF1D,gBAAgB,CAACD,OAAO,EAAE;MACxBhB,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;IAChB,CAAC,CAAC;IACF;IACA3B,KAAK,CAACyD,OAAO,CAAC,8BAA8B,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM8C,mBAAmB,GAAGA,CAAC7D,OAAO,EAAEgC,MAAM,KAAK;IAC/C,MAAMC,YAAY,GAAGlC,aAAa,CAACC,OAAO,CAAC;IAC3C,MAAM8D,gBAAgB,GAAG7B,YAAY,CAAC9C,cAAc;IACpD,MAAM4E,YAAY,GAAGD,gBAAgB,CAACvD,QAAQ,CAACyB,MAAM,CAAC,GAClD8B,gBAAgB,CAACxD,MAAM,CAAEvC,EAAE,IAAKA,EAAE,KAAKiE,MAAM,CAAC,GAC9C,CAAC,GAAG8B,gBAAgB,EAAE9B,MAAM,CAAC;IAEjC/B,gBAAgB,CAACD,OAAO,EAAE;MAAEb,cAAc,EAAE4E;IAAa,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIhE,OAAO,IAAK;IACtC,MAAMiC,YAAY,GAAGlC,aAAa,CAACC,OAAO,CAAC;IAC3C,MAAMiE,SAAS,GAAG,CAAAhG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAED,IAAI,KAAI,EAAE;IAEvC,IAAIiE,YAAY,CAAC9C,cAAc,CAACO,MAAM,KAAKuE,SAAS,CAACvE,MAAM,EAAE;MAC3DO,gBAAgB,CAACD,OAAO,EAAE;QAAEb,cAAc,EAAE;MAAG,CAAC,CAAC;IACnD,CAAC,MAAM;MACLc,gBAAgB,CAACD,OAAO,EAAE;QACxBb,cAAc,EAAE8E,SAAS,CAACrE,GAAG,CAAEsE,IAAI,IAAKA,IAAI,CAACnG,EAAE;MACjD,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMoG,WAAW,GAAG,MAAOnE,OAAO,IAAK;IACrC,IAAI;MAAA,IAAAoE,eAAA,EAAAC,gBAAA;MACF,MAAMpC,YAAY,GAAGlC,aAAa,CAACC,OAAO,CAAC;MAC3C,MAAMiE,SAAS,GAAG,CAAAhG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAED,IAAI,KAAI,EAAE;MACvC,MAAMsG,aAAa,GAAGL,SAAS,CAAC3D,MAAM,CAAE4D,IAAI,IAC1CjC,YAAY,CAAC9C,cAAc,CAACoB,QAAQ,CAAC2D,IAAI,CAACnG,EAAE,CAC9C,CAAC;MAED,IAAIuG,aAAa,CAAC5E,MAAM,KAAK,CAAC,EAAE;QAC9BpC,KAAK,CAACsD,KAAK,CAAC,+CAA+C,CAAC;QAC5D;MACF;MAEA,MAAM2D,eAAe,GAAG,CAAA1G,QAAQ,aAARA,QAAQ,wBAAAuG,eAAA,GAARvG,QAAQ,CAAEyD,KAAK,cAAA8C,eAAA,wBAAAC,gBAAA,GAAfD,eAAA,CAAkB,CAAC,CAAC,cAAAC,gBAAA,uBAApBA,gBAAA,CAAsBxF,IAAI,KAAI,WAAW;MACjEvB,KAAK,CAACyD,OAAO,CACX,uBAAuBuD,aAAa,CAAC5E,MAAM,qBAAqB6E,eAAe,WACjF,CAAC;MACDtE,gBAAgB,CAACD,OAAO,EAAE;QAAEb,cAAc,EAAE;MAAG,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDtD,KAAK,CAACsD,KAAK,CAAC,8CAA8C,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAM4D,wBAAwB,GAAG,MAAOxE,OAAO,IAAK;IAClD,MAAMiC,YAAY,GAAGlC,aAAa,CAACC,OAAO,CAAC;IAE3C,IAAIiC,YAAY,CAAC9C,cAAc,CAACO,MAAM,KAAK,CAAC,EAAE;MAC5CpC,KAAK,CAACsD,KAAK,CAAC,2CAA2C,CAAC;MACxD;IACF;IAEA,MAAMqD,SAAS,GAAG9D,YAAY,CAACH,OAAO,CAAC;IACvC,MAAMyE,cAAc,GAAGxC,YAAY,CAAC9C,cAAc,CAACmB,MAAM,CAAE0B,MAAM,IAAK;MACpE,MAAMkC,IAAI,GAAGD,SAAS,CAAC1B,IAAI,CAAEmC,CAAC,IAAKA,CAAC,CAAC3G,EAAE,KAAKiE,MAAM,CAAC;MACnD,OAAOkC,IAAI,IAAIA,IAAI,CAACS,OAAO,KAAK7G,aAAa;IAC/C,CAAC,CAAC;IAEF,IAAI2G,cAAc,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC/BpC,KAAK,CAACsD,KAAK,CAAC,wDAAwD,CAAC;MACrE;IACF;IAEA3D,iBAAiB,CAAC;MAChBqG,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAI;UACF,MAAMsB,OAAO,CAACC,GAAG,CACfJ,cAAc,CAAC7E,GAAG,CAAEoC,MAAM,IACxBvD,qBAAqB,CAACuD,MAAM,CAAC,CAACkB,MAAM,CAAC,CACvC,CACF,CAAC;;UAED;UACAuB,cAAc,CAAClD,OAAO,CAAES,MAAM,IAAK;YACjCtB,uBAAuB,CAACV,OAAO,EAAEgC,MAAM,CAAC;UAC1C,CAAC,CAAC;UAEF/B,gBAAgB,CAACD,OAAO,EAAE;YAAEb,cAAc,EAAE;UAAG,CAAC,CAAC;UACjDhB,OAAO,CAAC,CAAC;UACTb,KAAK,CAACyD,OAAO,CACX,wBAAwB0D,cAAc,CAAC/E,MAAM,oCAC/C,CAAC;QACH,CAAC,CAAC,OAAOkB,KAAK,EAAE;UACdoC,OAAO,CAACpC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxDtD,KAAK,CAACsD,KAAK,CACT,2DACF,CAAC;QACH;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAI1C,SAAS,EAAE;IACb,oBACEV,OAAA;MAAKsH,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDvH,OAAA;QAAKsH,SAAS,EAAC;MAA6D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChF,CAAC;EAEV;EAEA,oBACE3H,OAAA;IAAKsH,SAAS,EAAC,2BAA2B;IAAAC,QAAA,EAEvCpG,MAAM,CAACiB,GAAG,CAAEiB,KAAK,IAAK;MACrB,MAAMuE,UAAU,GAAGrF,aAAa,CAACc,KAAK,CAAC9C,EAAE,CAAC;MAC1C,MAAMsH,WAAW,GAAGrE,cAAc,CAAC,CAAC;MACpC,MAAMiD,SAAS,GAAG9D,YAAY,CAACU,KAAK,CAAC9C,EAAE,CAAC;MAExC,oBACEP,OAAA;QAAoBsH,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAElCvH,OAAA;UAAKsH,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDvH,OAAA;YAAKsH,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GAEzCK,UAAU,CAAChG,aAAa,gBACvB5B,OAAA;cACE8H,IAAI,EAAC,MAAM;cACXjD,KAAK,EAAE+C,UAAU,CAAC/F,WAAY;cAC9BkG,QAAQ,EAAGC,CAAC,IACVvF,gBAAgB,CAACY,KAAK,CAAC9C,EAAE,EAAE;gBACzBsB,WAAW,EAAEmG,CAAC,CAACC,MAAM,CAACpD;cACxB,CAAC,CACF;cACDqD,MAAM,EAAEA,CAAA,KACNzF,gBAAgB,CAACY,KAAK,CAAC9C,EAAE,EAAE;gBAAEqB,aAAa,EAAE;cAAM,CAAC,CACpD;cACDuG,SAAS,EAAGH,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACI,GAAG,KAAK,OAAO,EAAE;kBACrB3F,gBAAgB,CAACY,KAAK,CAAC9C,EAAE,EAAE;oBAAEqB,aAAa,EAAE;kBAAM,CAAC,CAAC;gBACtD;cACF,CAAE;cACF0F,SAAS,EAAC,sLAAsL;cAChMe,SAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAEF3H,OAAA;cACEsH,SAAS,EAAC,0FAA0F;cACpGgB,OAAO,EAAEA,CAAA,KACP7F,gBAAgB,CAACY,KAAK,CAAC9C,EAAE,EAAE;gBAAEqB,aAAa,EAAE;cAAK,CAAC,CACnD;cACDsD,KAAK,EAAC,0BAA0B;cAAAqC,QAAA,EAE/BK,UAAU,CAAC/F;YAAW;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACL,eAGD3H,OAAA;cACEsI,OAAO,EAAEA,CAAA,KAAM3B,WAAW,CAACtD,KAAK,CAAC9C,EAAE,CAAE;cACrC+G,SAAS,EAAC,sLAAsL;cAChMpC,KAAK,EAAC,iBAAiB;cAAAqC,QAAA,eAEvBvH,OAAA;gBAAMsH,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGT3H,OAAA;cACEsI,OAAO,EAAEA,CAAA,KAAMtB,wBAAwB,CAAC3D,KAAK,CAAC9C,EAAE,CAAE;cAClD+G,SAAS,EAAC,mLAAmL;cAC7LpC,KAAK,EAAC,yBAAyB;cAAAqC,QAAA,eAE/BvH,OAAA;gBAAMsH,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAcT3H,OAAA;cAAKsH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAC/CM,WAAW,CAACvD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClC,GAAG,CAAC,CAACgC,MAAM,EAAEmE,KAAK;gBAAA,IAAAC,aAAA,EAAAC,aAAA;gBAAA,oBACzCzI,OAAA;kBAAiBsH,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAClCnD,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEP,KAAK,gBACZ7D,OAAA;oBACE0I,GAAG,EAAEtE,MAAM,CAACP,KAAM;oBAClB8E,GAAG,EAAE,GAAGvE,MAAM,CAACT,KAAK,IAAIS,MAAM,CAACR,KAAK,EAAG;oBACvC0D,SAAS,EAAC;kBAAmE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,gBAEF3H,OAAA;oBAAKsH,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,GAC9InD,MAAM,aAANA,MAAM,wBAAAoE,aAAA,GAANpE,MAAM,CAAET,KAAK,cAAA6E,aAAA,uBAAbA,aAAA,CAAeI,MAAM,CAAC,CAAC,CAAC,EACxBxE,MAAM,aAANA,MAAM,wBAAAqE,aAAA,GAANrE,MAAM,CAAER,KAAK,cAAA6E,aAAA,uBAAbA,aAAA,CAAeG,MAAM,CAAC,CAAC,CAAC;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBACN,GAZOY,KAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAaV,CAAC;cAAA,CACP,CAAC,EACDE,WAAW,CAAC3F,MAAM,GAAG,CAAC,iBACrBlC,OAAA;gBAAKsH,SAAS,EAAC,qIAAqI;gBAAAC,QAAA,GAAC,GAClJ,EAACM,WAAW,CAAC3F,MAAM,GAAG,CAAC;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3H,OAAA;YACEsI,OAAO,EAAEA,CAAA,KAAM;cACb7F,gBAAgB,CAACY,KAAK,CAAC9C,EAAE,EAAE;gBACzBkB,YAAY,EAAE,IAAI;gBAClBD,WAAW,EAAE,CAACoG,UAAU,CAACpG;cAC3B,CAAC,CAAC;YACJ,CAAE;YACF8F,SAAS,EAAC,waAAwa;YAAAC,QAAA,gBAElbvH,OAAA;cACEsH,SAAS,EAAC,cAAc;cACxBuB,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,cAAc;cACrBC,OAAO,EAAC,WAAW;cAAAxB,QAAA,eAEnBvH,OAAA;gBACEgJ,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAA4B;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,qBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLC,UAAU,CAACpG,WAAW,iBACrBxB,OAAA;UAAKsH,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpEvH,OAAA,CAACR,mBAAmB;YAClB4J,QAAQ,EAAEA,CAAA,KAAM;cACd3G,gBAAgB,CAACY,KAAK,CAAC9C,EAAE,EAAE;gBACzBiB,WAAW,EAAE,KAAK;gBAClBC,YAAY,EAAE;cAChB,CAAC,CAAC;YACJ,CAAE;YACF4H,SAAS,EAAGlD,aAAa,IACvBD,qBAAqB,CAAC7C,KAAK,CAAC9C,EAAE,EAAE4F,aAAa,CAC9C;YACDjG,iBAAiB,EAAEA,iBAAkB;YACrCC,gBAAgB,EAAEA,gBAAiB;YACnCmJ,QAAQ,EACN1B,UAAU,CAACnG,YAAY,GACnBgF,SAAS,CAAC1B,IAAI,CACX1C,IAAI,IAAKA,IAAI,CAAC9B,EAAE,KAAKqH,UAAU,CAACnG,YACnC,CAAC,GACD;UACL;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD3H,OAAA;UAAKsH,SAAS,EAAC,4CAA4C;UAAAC,QAAA,eACzDvH,OAAA;YAAKsH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7Bd,SAAS,CAACvE,MAAM,KAAK,CAAC,gBACrBlC,OAAA;cAAKsH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCvH,OAAA;gBAAGsH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAGlC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,gBAEN3H,OAAA;cAAOsH,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACzCvH,OAAA;gBAAOsH,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eACpDvH,OAAA;kBAAAuH,QAAA,gBACEvH,OAAA;oBAAIuJ,KAAK,EAAC,KAAK;oBAACjC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,eACxCvH,OAAA;sBACE8H,IAAI,EAAC,UAAU;sBACf0B,OAAO,EACL5B,UAAU,CAACjG,cAAc,CAACO,MAAM,KAC9BuE,SAAS,CAACvE,MAAM,IAAIuE,SAAS,CAACvE,MAAM,GAAG,CAC1C;sBACD6F,QAAQ,EAAEA,CAAA,KAAMvB,kBAAkB,CAACnD,KAAK,CAAC9C,EAAE,CAAE;sBAC7C+G,SAAS,EAAC;oBAA+E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACL3H,OAAA;oBACEuJ,KAAK,EAAC,KAAK;oBACXjC,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,eAE1FvH,OAAA;sBAAKsH,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,GAAC,OAEjC,eAAAvH,OAAA;wBACEsH,SAAS,EAAC,4BAA4B;wBACtCuB,IAAI,EAAC,cAAc;wBACnBE,OAAO,EAAC,WAAW;wBAAAxB,QAAA,eAEnBvH,OAAA;0BACEyJ,QAAQ,EAAC,SAAS;0BAClBN,CAAC,EAAC,oHAAoH;0BACtHO,QAAQ,EAAC;wBAAS;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL3H,OAAA;oBACEuJ,KAAK,EAAC,KAAK;oBACXjC,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,eAE1FvH,OAAA;sBAAKsH,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,GAAC,WAEjC,eAAAvH,OAAA;wBACEsH,SAAS,EAAC,4BAA4B;wBACtCuB,IAAI,EAAC,cAAc;wBACnBE,OAAO,EAAC,WAAW;wBAAAxB,QAAA,eAEnBvH,OAAA;0BACEyJ,QAAQ,EAAC,SAAS;0BAClBN,CAAC,EAAC,oHAAoH;0BACtHO,QAAQ,EAAC;wBAAS;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL3H,OAAA;oBACEuJ,KAAK,EAAC,KAAK;oBACXjC,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC3F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL3H,OAAA;oBACEuJ,KAAK,EAAC,KAAK;oBACXjC,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC3F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL3H,OAAA;oBACEuJ,KAAK,EAAC,KAAK;oBACXjC,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC3F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL3H,OAAA;oBACEuJ,KAAK,EAAC,KAAK;oBACXjC,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC3F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL3H,OAAA;oBACEuJ,KAAK,EAAC,KAAK;oBACXjC,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC3F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR3H,OAAA;gBAAOsH,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDd,SAAS,CAACrE,GAAG,CAAEsE,IAAI,iBAClB1G,OAAA,CAAC2J,QAAQ;kBAEPjD,IAAI,EAAEA,IAAK;kBACXlE,OAAO,EAAEa,KAAK,CAAC9C,EAAG;kBAClBqJ,SAAS,EAAEhC,UAAU,CAACnG,YAAY,KAAKiF,IAAI,CAACnG,EAAG;kBAC/CsJ,WAAW,EAAEjC,UAAU,CAACjG,cAAc,CAACoB,QAAQ,CAC7C2D,IAAI,CAACnG,EACP,CAAE;kBACFuJ,eAAe,EAAElC,UAAU,CAAClG,gBAAgB,CAACgF,IAAI,CAACnG,EAAE,CAAE;kBACtDD,aAAa,EAAEA,aAAc;kBAC7ByJ,MAAM,EAAErF,UAAW;kBACnBsF,QAAQ,EAAEnE,YAAa;kBACvBoE,gBAAgB,EAAE1F,wBAAyB;kBAC3C2F,iBAAiB,EAAE7D,mBAAoB;kBACvC8D,gBAAgB,EAAExF;gBAAqB,GAblC+B,IAAI,CAACnG,EAAE;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcb,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3H,OAAA;UAAKsH,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCvH,OAAA;YACEsI,OAAO,EAAEA,CAAA,KAAM;cACb7F,gBAAgB,CAACY,KAAK,CAAC9C,EAAE,EAAE;gBACzBkB,YAAY,EAAE,IAAI;gBAClBD,WAAW,EAAE;cACf,CAAC,CAAC;YACJ,CAAE;YACF8F,SAAS,EAAC,6HAA6H;YAAAC,QAAA,gBAGvIvH,OAAA;cAAKsH,SAAS,EAAC,6EAA6E;cAAAC,QAAA,eAC1FvH,OAAA;gBAAMsH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAGN3H,OAAA;cAAMsH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GAjSEtE,KAAK,CAAC9C,EAAE;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkSb,CAAC;IAEV,CAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAvH,EAAA,CAjsBMH,kBAAkB;EAAA,QAUlBN,8BAA8B,EAQFC,gCAAgC,EAChCC,gCAAgC;AAAA;AAAAuK,EAAA,GAnB5DnK,kBAAkB;AAksBxB,MAAM0J,QAAQ,GAAGA,CAAC;EAChBjD,IAAI;EACJlE,OAAO;EACPoH,SAAS;EACTC,WAAW;EACXC,eAAe;EACfxJ,aAAa;EACbyJ,MAAM;EACNC,QAAQ;EACRC,gBAAgB;EAChBC,iBAAiB;EACjBC;AACF,CAAC,KAAK;EAAAE,GAAA;EAAA,IAAAC,UAAA,EAAAC,gBAAA,EAAAC,IAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,iBAAA;EACJ;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvL,QAAQ,CAAC;IAC3C4F,KAAK,EAAEwB,IAAI,CAACzB,cAAc,IAAIyB,IAAI,CAACxB,KAAK,IAAIwB,IAAI,CAACvB,SAAS,IAAI,EAAE;IAChEC,QAAQ,EAAEsB,IAAI,CAACtB,QAAQ,IAAIsB,IAAI,CAACvB,SAAS,IAAI,EAAE;IAC/CnB,IAAI,EAAE,EAAAsG,UAAA,GAAA5D,IAAI,CAAC1C,IAAI,cAAAsG,UAAA,uBAATA,UAAA,CAAWjJ,IAAI,KAAI,EAAE;IAC3ByJ,UAAU,EAAE,EAAAP,gBAAA,GAAA7D,IAAI,CAACoE,UAAU,cAAAP,gBAAA,uBAAfA,gBAAA,CAAiBlJ,IAAI,KAAI;EACvC,CAAC,CAAC;EAEF,MAAM0J,iBAAiB,GAAGA,CAACnG,KAAK,EAAEC,KAAK,KAAK;IAC1CgG,aAAa,CAAEvI,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACsC,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMmG,eAAe,GAAIpG,KAAK,IAAK;IAAA,IAAAqG,WAAA,EAAAC,iBAAA;IACjC,MAAMC,aAAa,GACjBvG,KAAK,KAAK,OAAO,GACb8B,IAAI,CAACzB,cAAc,IAAIyB,IAAI,CAACxB,KAAK,IAAIwB,IAAI,CAACvB,SAAS,GACnDP,KAAK,KAAK,UAAU,GACpB8B,IAAI,CAACtB,QAAQ,IAAIsB,IAAI,CAACvB,SAAS,GAC/BP,KAAK,KAAK,MAAM,IAAAqG,WAAA,GAChBvE,IAAI,CAAC1C,IAAI,cAAAiH,WAAA,uBAATA,WAAA,CAAW5J,IAAI,GACfuD,KAAK,KAAK,YAAY,IAAAsG,iBAAA,GACtBxE,IAAI,CAACoE,UAAU,cAAAI,iBAAA,uBAAfA,iBAAA,CAAiB7J,IAAI,GACrB,EAAE;IAER,IACEuJ,UAAU,CAAChG,KAAK,CAAC,KAAKuG,aAAa,IACnCP,UAAU,CAAChG,KAAK,CAAC,CAACwG,IAAI,CAAC,CAAC,KAAK,EAAE,EAC/B;MACAjB,gBAAgB,CAAC3H,OAAO,EAAEkE,IAAI,CAACnG,EAAE,EAAEqE,KAAK,EAAEgG,UAAU,CAAChG,KAAK,CAAC,CAAC;IAC9D;EACF,CAAC;EAED,MAAMyG,aAAa,GAAIrD,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACI,GAAG,KAAK,OAAO,EAAE;MACrBJ,CAAC,CAACC,MAAM,CAACqD,IAAI,CAAC,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,QAAQA,KAAK;MACX,KAAK,QAAQ;QACX,OAAO,qDAAqD;MAC9D,KAAK,UAAU;QACb,OAAO,wDAAwD;MACjE,KAAK,MAAM;QACT,OAAO,+CAA+C;MACxD;QACE,OAAO,qDAAqD;IAChE;EACF,CAAC;EAED,oBACExL,OAAA;IAAIsH,SAAS,EAAC,oCAAoC;IAAAC,QAAA,gBAChDvH,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,eACvBvH,OAAA;QACE8H,IAAI,EAAC,UAAU;QACf0B,OAAO,EAAEK,WAAY;QACrB9B,QAAQ,EAAEA,CAAA,KAAMmC,iBAAiB,CAAC1H,OAAO,EAAEkE,IAAI,CAACnG,EAAE,CAAE;QACpD+G,SAAS,EAAC;MAA+E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGL3H,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,eACvBvH,OAAA;QAAKsH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhCvH,OAAA;UAAKsH,SAAS,EAAC,yGAAyG;UAAAC,QAAA,EACrH,EAAAiD,IAAA,GAAC9D,IAAI,CAACzB,cAAc,IAAIyB,IAAI,CAACxB,KAAK,IAAIwB,IAAI,CAACvB,SAAS,cAAAqF,IAAA,wBAAAC,WAAA,GAApDD,IAAA,CACG5B,MAAM,CAAC,CAAC,CAAC,cAAA6B,WAAA,uBADZA,WAAA,CAEGgB,WAAW,CAAC,CAAC,KAAI;QAAG;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,EACLiC,SAAS,gBACR5J,OAAA;UACE8H,IAAI,EAAC,MAAM;UACXjD,KAAK,EAAE+F,UAAU,CAAC1F,KAAM;UACxB6C,QAAQ,EAAGC,CAAC,IAAK+C,iBAAiB,CAAC,OAAO,EAAE/C,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;UAC5DqD,MAAM,EAAEA,CAAA,KAAM8C,eAAe,CAAC,OAAO,CAAE;UACvC7C,SAAS,EAAEkD,aAAc;UACzB/D,SAAS,EAAC,kIAAkI;UAC5Ie,SAAS;UACTqD,WAAW,EAAC;QAAwB;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,gBAEF3H,OAAA;UAAMsH,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACxCb,IAAI,CAACzB,cAAc,IAAIyB,IAAI,CAACxB,KAAK,IAAIwB,IAAI,CAACvB;QAAS;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGL3H,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,eACvBvH,OAAA;QAAKsH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/BqC,SAAS,gBACR5J,OAAA;UACE8H,IAAI,EAAC,MAAM;UACXjD,KAAK,EAAE+F,UAAU,CAACxF,QAAS;UAC3B2C,QAAQ,EAAGC,CAAC,IAAK+C,iBAAiB,CAAC,UAAU,EAAE/C,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;UAC/DqD,MAAM,EAAEA,CAAA,KAAM8C,eAAe,CAAC,UAAU,CAAE;UAC1C7C,SAAS,EAAEkD,aAAc;UACzB/D,SAAS,EAAC,2HAA2H;UACrIoE,WAAW,EAAC;QAAmB;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,gBAEF3H,OAAA;UAAMsH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EACjCb,IAAI,CAACtB,QAAQ,IAAIsB,IAAI,CAACvB;QAAS;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACP,eAED3H,OAAA;UACEsH,SAAS,EAAC,sLAAsL;UAChMpC,KAAK,EAAC,eAAe;UACrBoD,OAAO,EAAEA,CAAA,KAAM;YACb,MAAMlD,QAAQ,GAAGsB,IAAI,CAACtB,QAAQ,IAAIsB,IAAI,CAACvB,SAAS;YAChD,IAAIC,QAAQ,EAAE;cACZuG,SAAS,CAACC,SAAS,CAACC,SAAS,CAACzG,QAAQ,CAAC;cACvCtF,KAAK,CAACyD,OAAO,CAAC,+BAA+B,CAAC;YAChD;UACF,CAAE;UAAAgE,QAAA,eAEFvH,OAAA;YAAMsH,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGL3H,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,eACvBvH,OAAA;QAAKsH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvH,OAAA;UAAMsH,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAC3CuC,eAAe,GAAGpD,IAAI,CAACrB,QAAQ,GAAG;QAAc;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEP3H,OAAA;UACEsI,OAAO,EAAEA,CAAA,KAAM2B,gBAAgB,CAACzH,OAAO,EAAEkE,IAAI,CAACnG,EAAE,CAAE;UAClD+G,SAAS,EAAC,wLAAwL;UAClMpC,KAAK,EAAE4E,eAAe,GAAG,eAAe,GAAG,eAAgB;UAAAvC,QAAA,eAE3DvH,OAAA;YAAMsH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAChDuC,eAAe,GAAG,gBAAgB,GAAG;UAAY;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAET3H,OAAA;UACEsH,SAAS,EAAC,sLAAsL;UAChMpC,KAAK,EAAC,eAAe;UACrBoD,OAAO,EAAEA,CAAA,KAAM;YACb,IAAI5B,IAAI,CAACrB,QAAQ,EAAE;cACjBsG,SAAS,CAACC,SAAS,CAACC,SAAS,CAACnF,IAAI,CAACrB,QAAQ,CAAC;cAC5CvF,KAAK,CAACyD,OAAO,CAAC,+BAA+B,CAAC;YAChD;UACF,CAAE;UAAAgE,QAAA,eAEFvH,OAAA;YAAMsH,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGL3H,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,EACtBqC,SAAS,gBACR5J,OAAA;QACE8H,IAAI,EAAC,MAAM;QACXjD,KAAK,EAAE+F,UAAU,CAAC5G,IAAK;QACvB+D,QAAQ,EAAGC,CAAC,IAAK+C,iBAAiB,CAAC,MAAM,EAAE/C,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;QAC3DqD,MAAM,EAAEA,CAAA,KAAM8C,eAAe,CAAC,MAAM,CAAE;QACtC7C,SAAS,EAAEkD,aAAc;QACzB/D,SAAS,EAAC;MAAsH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjI,CAAC,gBAEF3H,OAAA;QAAMsH,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC5B,EAAAmD,WAAA,GAAAhE,IAAI,CAAC1C,IAAI,cAAA0G,WAAA,uBAATA,WAAA,CAAWrJ,IAAI,KAAI;MAAW;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGL3H,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,EACtBqC,SAAS,gBACR5J,OAAA;QACE8H,IAAI,EAAC,MAAM;QACXjD,KAAK,EAAE+F,UAAU,CAACE,UAAW;QAC7B/C,QAAQ,EAAGC,CAAC,IAAK+C,iBAAiB,CAAC,YAAY,EAAE/C,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;QACjEqD,MAAM,EAAEA,CAAA,KAAM8C,eAAe,CAAC,YAAY,CAAE;QAC5C7C,SAAS,EAAEkD,aAAc;QACzB/D,SAAS,EAAC;MAAsH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjI,CAAC,gBAEF3H,OAAA;QAAMsH,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC5B,EAAAoD,iBAAA,GAAAjE,IAAI,CAACoE,UAAU,cAAAH,iBAAA,uBAAfA,iBAAA,CAAiBtJ,IAAI,KAAI;MAAiB;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGL3H,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,eACvBvH,OAAA;QACEsH,SAAS,EAAE,yCAAyCiE,wBAAwB,CAC1E7E,IAAI,CAAC8E,KACP,CAAC,EAAG;QAAAjE,QAAA,EAEHb,IAAI,CAAC8E,KAAK,IAAI;MAAiB;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGL3H,OAAA;MAAIsH,SAAS,EAAC,WAAW;MAAAC,QAAA,eACvBvH,OAAA;QAAKsH,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAG1CvH,OAAA;UACEsI,OAAO,EAAEA,CAAA,KAAMyB,MAAM,CAACvH,OAAO,EAAEkE,IAAI,CAACnG,EAAE,CAAE;UACxC+G,SAAS,EAAC,sLAAsL;UAChMpC,KAAK,EAAC,MAAM;UAAAqC,QAAA,eAEZvH,OAAA;YAAMsH,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGRjB,IAAI,CAACS,OAAO,KAAK7G,aAAa,iBAC7BN,OAAA;UACEsI,OAAO,EAAEA,CAAA,KAAM0B,QAAQ,CAACtD,IAAI,CAACnG,EAAE,CAAE;UACjC+G,SAAS,EAAC,mLAAmL;UAC7LpC,KAAK,EAAC,QAAQ;UAAAqC,QAAA,eAEdvH,OAAA;YAAMsH,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAET,CAAC;AAAC0C,GAAA,CA9PIV,QAAQ;AAAAmC,GAAA,GAARnC,QAAQ;AAgQd,eAAe1J,kBAAkB;AAAC,IAAAmK,EAAA,EAAA0B,GAAA;AAAAC,YAAA,CAAA3B,EAAA;AAAA2B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}