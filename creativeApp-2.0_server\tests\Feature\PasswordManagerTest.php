<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\Route;

class PasswordManagerTest extends TestCase
{

    public function test_password_manager_routes_exist()
    {
        // Test that routes are registered by checking if they exist in the route collection
        $routes = Route::getRoutes();
        $passwordManagerRoutes = [];

        foreach ($routes as $route) {
            if (str_contains($route->uri(), 'password-managers')) {
                $passwordManagerRoutes[] = $route->uri();
            }
        }

        $this->assertNotEmpty($passwordManagerRoutes, 'Password Manager routes should be registered');
        $this->assertContains('api/password-managers', $passwordManagerRoutes);
    }

    public function test_password_manager_controller_exists()
    {
        // Test that the controller class exists
        $this->assertTrue(class_exists('App\Http\Controllers\PasswordManagerController'));
    }

    public function test_password_manager_model_exists()
    {
        // Test that the model class exists
        $this->assertTrue(class_exists('App\Models\PasswordManager'));
    }
}
