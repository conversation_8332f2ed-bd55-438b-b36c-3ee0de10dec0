<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('password_managers', function (Blueprint $table) {
            $table->id();
            
            // Foreign key columns (nullable)
            $table->unsignedBigInteger('department_id')->nullable();
            $table->unsignedBigInteger('team_id')->nullable();
            $table->unsignedBigInteger('user_id');
            
            // Main data columns
            $table->string('user_name');
            $table->text('password'); // Will store encrypted password
            
            // Laravel standard timestamps
            $table->timestamps();
            
            // Foreign key constraints (only if tables exist)
            if (Schema::hasTable('users')) {
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            }
            if (Schema::hasTable('departments')) {
                $table->foreign('department_id')->references('id')->on('departments')->onDelete('set null');
            }
            if (Schema::hasTable('teams')) {
                $table->foreign('team_id')->references('id')->on('teams')->onDelete('set null');
            }
            
            // Indexes for performance
            $table->index(['user_id']);
            $table->index(['department_id', 'team_id']);
            $table->index('user_name');
            $table->index('password');

            
            // Unique constraint for password title per user
            $table->unique(['user_id', 'user_name']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('password_managers');
    }
};
