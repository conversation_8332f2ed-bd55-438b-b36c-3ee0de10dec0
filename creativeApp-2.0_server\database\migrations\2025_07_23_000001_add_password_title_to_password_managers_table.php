<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('password_managers', function (Blueprint $table) {
            if (!Schema::hasColumn('password_managers', 'password_title')) {
                $table->string('password_title')->after('user_id');
            }
            if (!Schema::hasColumn('password_managers', 'username')) {
                $table->string('username')->after('password_title');
            }
        });

        // Migrate existing data: copy user_name to both password_title and username
        DB::table('password_managers')->get()->each(function ($record) {
            DB::table('password_managers')
                ->where('id', $record->id)
                ->update([
                    'password_title' => $record->password_title ?: $record->user_name,
                    'username' => $record->username ?: $record->user_name,
                ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('password_managers', function (Blueprint $table) {
            $table->dropColumn(['password_title', 'username']);
        });
    }
};
