<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

trait HasRoleCheck
{
    /**
     * Check if the authenticated user has any of the specified roles
     *
     * @param array $roles
     * @return bool
     */
    protected function userHasRole(array $roles): bool
    {
        // Disabled by default - can be enabled when Spatie/Permission is implemented
        // Uncomment below when roles are properly configured:
        // $user = $this->getAuthenticatedUser();
        // return $user && $user->roles()->whereIn('name', $roles)->exists();
        return false;
    }

    /**
     * Check if the authenticated user is admin or super-admin
     *
     * @return bool
     */
    protected function userIsAdmin(): bool
    {
        // Disabled by default - can be enabled when roles are properly configured
        // Uncomment below when admin roles are properly set up:
        // $user = $this->getAuthenticatedUser();
        // return $user && $user->roles()->whereIn('name', ['admin', 'super-admin'])->exists();
        return false;
    }

    /**
     * Get the authenticated user
     *
     * @return \App\Models\User|null
     */
    protected function getAuthenticatedUser()
    {
        return Auth::user();
    }
}
