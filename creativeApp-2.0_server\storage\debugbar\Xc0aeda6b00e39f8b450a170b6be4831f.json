{"__meta": {"id": "Xc0aeda6b00e39f8b450a170b6be4831f", "datetime": "2025-07-24 17:41:18", "utime": **********.689836, "method": "POST", "uri": "/api/password-managers", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.214458, "end": **********.689848, "duration": 0.4753899574279785, "duration_str": "475ms", "measures": [{"label": "Booting", "start": **********.214458, "relative_start": 0, "end": **********.634667, "relative_end": **********.634667, "duration": 0.4202089309692383, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.634674, "relative_start": 0.42021608352661133, "end": **********.689849, "relative_end": 9.5367431640625e-07, "duration": 0.055174827575683594, "duration_str": "55.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23997112, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/password-managers", "middleware": "api, auth:sanctum, cors, verified", "as": "password-managers.store", "controller": "App\\Http\\Controllers\\PasswordManagerController@store", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FPasswordManagerController.php&line=87\" onclick=\"\">app/Http/Controllers/PasswordManagerController.php:87-124</a>"}, "queries": {"nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0069500000000000004, "accumulated_duration_str": "6.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.649347, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '435' limit 1", "type": "query", "params": [], "bindings": ["435"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.6531851, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 22.158}, {"sql": "select * from `users` where `users`.`id` = 150 limit 1", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.658134, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "creative_app3", "explain": null, "start_percent": 22.158, "width_percent": 4.173}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-24 17:41:18', `personal_access_tokens`.`updated_at` = '2025-07-24 17:41:18' where `id` = 435", "type": "query", "params": [], "bindings": ["2025-07-24 17:41:18", "2025-07-24 17:41:18", 435], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.65986, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "creative_app3", "explain": null, "start_percent": 26.331, "width_percent": 35.252}, {"sql": "select count(*) as aggregate from `departments` where `id` = '7'", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "start": **********.672492, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "creative_app3", "explain": null, "start_percent": 61.583, "width_percent": 5.18}, {"sql": "insert into `password_managers` (`department_id`, `team_id`, `password_title`, `username`, `user_name`, `password`, `user_id`, `updated_at`, `created_at`) values ('7', '', 'Accuweather', '<EMAIL>', 'Accuweather', 'eyJpdiI6Ik54VTdCbGxRZVNTbkhGVXJqQ25zWmc9PSIsInZhbHVlIjoiUGd5MGdJbUlMMGdxTEZPNENRVVVoQTZsQVlnR1Y4V1B6cXY4LzJUN0swYnVLczJvOVdTRFVYNFBuV0IwOThKYlBnSkxDV3FoZUtKM0N4WGxQNHJpZ0E9PSIsIm1hYyI6ImU2NTU0NjAwYTdjM2VjZTE1YmFiYzlhODU2MjY0Y2ZhYjQ4OWQ5MmE1ZjgxYTkzOGMxNjAyZDY2NTA1NzIyMGIiLCJ0YWciOiIifQ==', 150, '2025-07-24 17:41:18', '2025-07-24 17:41:18')", "type": "query", "params": [], "bindings": ["7", null, "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eyJpdiI6Ik54VTdCbGxRZVNTbkhGVXJqQ25zWmc9PSIsInZhbHVlIjoiUGd5MGdJbUlMMGdxTEZPNENRVVVoQTZsQVlnR1Y4V1B6cXY4LzJUN0swYnVLczJvOVdTRFVYNFBuV0IwOThKYlBnSkxDV3FoZUtKM0N4WGxQNHJpZ0E9PSIsIm1hYyI6ImU2NTU0NjAwYTdjM2VjZTE1YmFiYzlhODU2MjY0Y2ZhYjQ4OWQ5MmE1ZjgxYTkzOGMxNjAyZDY2NTA1NzIyMGIiLCJ0YWciOiIifQ==", 150, "2025-07-24 17:41:18", "2025-07-24 17:41:18"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 108}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6776881, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "PasswordManagerController.php:108", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FPasswordManagerController.php&line=108", "ajax": false, "filename": "PasswordManagerController.php", "line": "108"}, "connection": "creative_app3", "explain": null, "start_percent": 66.763, "width_percent": 22.59}, {"sql": "select * from `departments` where `departments`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.680702, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PasswordManagerController.php:109", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FPasswordManagerController.php&line=109", "ajax": false, "filename": "PasswordManagerController.php", "line": "109"}, "connection": "creative_app3", "explain": null, "start_percent": 89.353, "width_percent": 4.604}, {"sql": "select * from `teams` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.682269, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "PasswordManagerController.php:109", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FPasswordManagerController.php&line=109", "ajax": false, "filename": "PasswordManagerController.php", "line": "109"}, "connection": "creative_app3", "explain": null, "start_percent": 93.957, "width_percent": 2.59}, {"sql": "select * from `users` where `users`.`id` in (150)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.683416, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PasswordManagerController.php:109", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/PasswordManagerController.php", "file": "C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\app\\Http\\Controllers\\PasswordManagerController.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FPasswordManagerController.php&line=109", "ajax": false, "filename": "PasswordManagerController.php", "line": "109"}, "connection": "creative_app3", "explain": null, "start_percent": 96.547, "width_percent": 3.453}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\Department": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/password-managers", "status_code": "<pre class=sf-dump id=sf-dump-1544227486 data-indent-pad=\"  \"><span class=sf-dump-num>201</span>\n</pre><script>Sfdump(\"sf-dump-1544227486\", {\"maxDepth\":0})</script>\n", "status_text": "Created", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-694104506 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-694104506\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-855182720 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>password_title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Accuweather</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>team_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Accuweather</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855182720\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-421609490 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">174</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 435|h******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421609490\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1316482955 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1316482955\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1844316830 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 24 Jul 2025 11:41:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">GET, POST, PUT, DELETE, OPTIONS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Content-Type, Authorization, X-Requested-With</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">147</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844316830\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1869681724 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1869681724\", {\"maxDepth\":0})</script>\n"}}